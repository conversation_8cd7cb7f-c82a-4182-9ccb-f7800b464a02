import { IAccessor } from '@univerjs/core';
import { IMenuButtonItem, IMenuItem, IMenuSelectorItem } from '@univerjs/ui';
export declare const SHEET_PERMISSION_CONTEXT_MENU_ID = "sheet.contextMenu.permission";
export declare function sheetPermissionToolbarMenuFactory(accessor: IAccessor): IMenuItem;
export declare function sheetPermissionContextMenuFactory(accessor: IAccessor): IMenuSelectorItem<string>;
export declare function sheetPermissionAddProtectContextMenuFactory(accessor: IAccessor): IMenuButtonItem;
export declare function sheetPermissionEditProtectContextMenuFactory(accessor: IAccessor): IMenuButtonItem;
export declare function sheetPermissionRemoveProtectContextMenuFactory(accessor: IAccessor): IMenuButtonItem;
export declare function sheetPermissionViewAllProtectRuleContextMenuFactory(accessor: IAccessor): IMenuButtonItem;
export declare function sheetPermissionProtectSheetInSheetBarMenuFactory(accessor: IAccessor): IMenuButtonItem;
export declare function sheetPermissionRemoveProtectionSheetBarMenuFactory(accessor: IAccessor): IMenuButtonItem;
export declare function sheetPermissionChangeSheetPermissionSheetBarMenuFactory(accessor: IAccessor): IMenuButtonItem;
export declare function sheetPermissionViewAllProtectRuleSheetBarMenuFactory(accessor: IAccessor): IMenuButtonItem;
