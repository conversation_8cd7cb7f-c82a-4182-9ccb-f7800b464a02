/**
 * 测试独立的 sheets-ui 包是否可以正常导入
 */

async function testStandaloneImport() {
    console.log('🧪 测试独立 sheets-ui 包导入...');
    
    try {
        // 尝试动态导入
        const module = await import('./standalone-sheets-ui/index.js');
        
        console.log('✅ 模块导入成功');
        
        // 检查是否有 UniverSheetsUIPlugin
        if (module.UniverSheetsUIPlugin) {
            console.log('✅ UniverSheetsUIPlugin 导出成功');
            console.log('📋 插件名称:', module.UniverSheetsUIPlugin.pluginName || 'SHEET_UI_PLUGIN');
            
            // 尝试创建插件实例
            try {
                const plugin = new module.UniverSheetsUIPlugin();
                console.log('✅ 插件实例创建成功');
                console.log('📦 插件类型:', typeof plugin);
            } catch (error) {
                console.log('⚠️  插件实例创建失败:', error.message);
            }
        } else {
            console.log('⚠️  UniverSheetsUIPlugin 未找到，可用导出:', Object.keys(module).slice(0, 10));
        }
        
        // 检查其他重要导出
        const importantExports = [
            'SheetUIController',
            'SheetRenderController', 
            'SheetsRenderService',
            'SheetSkeletonManagerService'
        ];
        
        console.log('\n📦 检查重要导出:');
        importantExports.forEach(exportName => {
            if (module[exportName]) {
                console.log(`  ✅ ${exportName}`);
            } else {
                console.log(`  ❌ ${exportName} 缺失`);
            }
        });
        
        console.log('\n🎉 独立包测试完成！');
        console.log('📝 使用方法:');
        console.log('```javascript');
        console.log('import { UniverSheetsUIPlugin } from "./standalone-sheets-ui";');
        console.log('univer.registerPlugin(UniverSheetsUIPlugin);');
        console.log('```');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error('详细错误:', error);
    }
}

// 运行测试
testStandaloneImport();
