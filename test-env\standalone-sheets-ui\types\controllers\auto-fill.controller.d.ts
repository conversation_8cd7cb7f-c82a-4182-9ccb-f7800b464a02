import { Disposable, ICommandService, Injector, IUniverInstanceService } from '@univerjs/core';
import { IRenderManagerService } from '@univerjs/engine-render';
import { IAutoFillService } from '../services/auto-fill/auto-fill.service';
import { IEditorBridgeService } from '../services/editor-bridge.service';
import { SheetsRenderService } from '../services/sheets-render.service';
export declare class AutoFillController extends Disposable {
    private readonly _univerInstanceService;
    private readonly _commandService;
    private readonly _autoFillService;
    private readonly _editorBridgeService;
    private readonly _renderManagerService;
    private readonly _injector;
    private _sheetsRenderService;
    private _beforeApplyData;
    private _currentLocation;
    private _copyData;
    private _defaultHook;
    constructor(_univerInstanceService: IUniverInstanceService, _commandService: ICommandService, _autoFillService: IAutoFillService, _editorBridgeService: IEditorBridgeService, _renderManagerService: IRenderManagerService, _injector: Injector, _sheetsRenderService: SheetsRenderService);
    private _init;
    private _initSkeletonChange;
    private _initDefaultHook;
    private _initQuitListener;
    private _quit;
    private _initSelectionControlFillChanged;
    private _handleDbClickFill;
    private _detectFillRange;
    private _getApplyData;
    private _applyFunctions;
    private _getCopyData;
    private _getEmptyCopyDataPiece;
    private _getMergeApplyData;
    private _presetAndCacheData;
    private _fillData;
    private _shouldDisableSeries;
    private _getPreferredApplyType;
}
