import { IShortcutItem } from '@univerjs/ui';
import { IExpandSelectionCommandParams, IMoveSelectionCommandParams, IMoveSelectionEnterAndTabCommandParams, ISelectAllCommandParams } from '../../commands/commands/set-selection.command';
export declare const MoveSelectionDownShortcutItem: IShortcutItem<IMoveSelectionCommandParams>;
export declare const MoveSelectionUpShortcutItem: IShortcutItem<IMoveSelectionCommandParams>;
export declare const MoveSelectionLeftShortcutItem: IShortcutItem<IMoveSelectionCommandParams>;
export declare const MoveSelectionRightShortcutItem: IShortcutItem<IMoveSelectionCommandParams>;
export declare const MoveSelectionTabShortcutItem: IShortcutItem<IMoveSelectionEnterAndTabCommandParams>;
export declare const MoveSelectionTabLeftShortcutItem: IShortcutItem<IMoveSelectionEnterAndTabCommandParams>;
export declare const MoveSelectionEnterShortcutItem: IShortcutItem<IMoveSelectionEnterAndTabCommandParams>;
export declare const MoveSelectionEnterUpShortcutItem: IShortcutItem<IMoveSelectionEnterAndTabCommandParams>;
export declare const MoveSelectionEndDownShortcutItem: IShortcutItem<IMoveSelectionCommandParams>;
export declare const MoveSelectionEndUpShortcutItem: IShortcutItem<IMoveSelectionCommandParams>;
export declare const MoveSelectionEndLeftShortcutItem: IShortcutItem<IMoveSelectionCommandParams>;
export declare const MoveSelectionEndRightShortcutItem: IShortcutItem<IMoveSelectionCommandParams>;
export declare const ExpandSelectionDownShortcutItem: IShortcutItem<IExpandSelectionCommandParams>;
export declare const ExpandSelectionUpShortcutItem: IShortcutItem<IExpandSelectionCommandParams>;
export declare const ExpandSelectionLeftShortcutItem: IShortcutItem<IExpandSelectionCommandParams>;
export declare const ExpandSelectionRightShortcutItem: IShortcutItem<IExpandSelectionCommandParams>;
export declare const ExpandSelectionEndDownShortcutItem: IShortcutItem<IExpandSelectionCommandParams>;
export declare const ExpandSelectionEndUpShortcutItem: IShortcutItem<IExpandSelectionCommandParams>;
export declare const ExpandSelectionEndLeftShortcutItem: IShortcutItem<IExpandSelectionCommandParams>;
export declare const ExpandSelectionEndRightShortcutItem: IShortcutItem<IExpandSelectionCommandParams>;
export declare const SelectAllShortcutItem: IShortcutItem<ISelectAllCommandParams>;
