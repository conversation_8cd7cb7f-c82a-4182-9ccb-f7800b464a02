import { IShortcutItem, KeyCode } from '@univerjs/ui';
export declare const ARROW_SELECTION_KEYCODE_LIST: KeyCode[];
export declare const MOVE_SELECTION_KEYCODE_LIST: KeyCode[];
export declare function generateArrowSelectionShortCutItem(): IShortcutItem<object>[];
export declare const StartEditWithF2Shortcut: IShortcutItem;
export declare const EditorCursorEnterShortcut: IShortcutItem;
export declare const EditorCursorTabShortcut: IShortcutItem;
export declare const EditorCursorEscShortcut: IShortcutItem;
export declare const EditorCursorCtrlEnterShortcut: IShortcutItem;
export declare const EditorBreakLineShortcut: IShortcutItem;
export declare const EditorDeleteLeftShortcut: IShortcutItem;
export declare const EditorDeleteRightShortcut: IShortcutItem;
export declare const ShiftEditorDeleteLeftShortcut: IShortcutItem;
export declare const EditorDeleteLeftShortcutInActive: IShortcutItem;
