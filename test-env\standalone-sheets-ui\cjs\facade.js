"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const h=require("@univerjs/core"),M=require("@univerjs/core/facade"),P=require("@univerjs/docs"),v=require("@univerjs/engine-render"),I=require("@univerjs/sheets"),c=require("@univerjs/sheets-ui"),p=require("@univerjs/sheets/facade"),m=require("@univerjs/ui"),C=require("rxjs");class H extends M.FUniver{_initSheetUIEvent(e){const r=e.get(h.ICommandService);this.registerEventHandler(this.Event.BeforeSheetEditStart,()=>r.beforeCommandExecuted(t=>{if(t.id!==c.SetCellEditVisibleOperation.id)return;const i=this.getActiveSheet();if(!i)return;const{workbook:o,worksheet:s}=i,l=e.get(c.IEditorBridgeService),u=t.params,{visible:g,keycode:S,eventType:a}=u,n=l.getEditLocation();if(g){const d={row:n.row,column:n.column,eventType:a,keycode:S,workbook:o,worksheet:s,isZenEditor:!1};if(this.fireEvent(this.Event.BeforeSheetEditStart,d),d.cancel)throw new h.CanceledError}})),this.registerEventHandler(this.Event.BeforeSheetEditEnd,()=>r.beforeCommandExecuted(t=>{if(t.id!==c.SetCellEditVisibleOperation.id)return;const i=this.getActiveSheet();if(!i)return;const{workbook:o,worksheet:s}=i,l=e.get(c.IEditorBridgeService),u=e.get(h.IUniverInstanceService),g=t.params,{visible:S,keycode:a,eventType:n}=g,d=l.getEditLocation();if(!S){const k={row:d.row,column:d.column,eventType:n,keycode:a,workbook:o,worksheet:s,isZenEditor:!1,value:h.RichTextValue.create(u.getUnit(h.DOCS_NORMAL_EDITOR_UNIT_ID_KEY).getSnapshot()),isConfirm:a!==m.KeyCode.ESC};if(this.fireEvent(this.Event.BeforeSheetEditEnd,k),k.cancel)throw new h.CanceledError}})),this.registerEventHandler(this.Event.SheetEditStarted,()=>r.onCommandExecuted(t=>{if(t.id!==c.SetCellEditVisibleOperation.id)return;const i=this.getCommandSheetTarget(t);if(!i)return;const{workbook:o,worksheet:s}=i,l=e.get(c.IEditorBridgeService),u=t.params,{visible:g,keycode:S,eventType:a}=u,n=l.getEditLocation();if(g){const d={row:n.row,column:n.column,eventType:a,keycode:S,workbook:o,worksheet:s,isZenEditor:!1};this.fireEvent(this.Event.SheetEditStarted,d)}})),this.registerEventHandler(this.Event.SheetEditEnded,()=>r.onCommandExecuted(t=>{if(t.id!==c.SetCellEditVisibleOperation.id)return;const i=this.getCommandSheetTarget(t);if(!i)return;const{workbook:o,worksheet:s}=i,l=e.get(c.IEditorBridgeService),u=t.params,{visible:g,keycode:S,eventType:a}=u,n=l.getEditLocation();if(!g){const d={row:n.row,column:n.column,eventType:a,keycode:S,workbook:o,worksheet:s,isZenEditor:!1,isConfirm:S!==m.KeyCode.ESC};this.fireEvent(this.Event.SheetEditEnded,d)}})),this.registerEventHandler(this.Event.SheetEditChanging,()=>r.onCommandExecuted(t=>{if(t.id!==P.RichTextEditingMutation.id)return;const i=this.getActiveSheet();if(!i)return;const{workbook:o,worksheet:s}=i,l=e.get(c.IEditorBridgeService),u=e.get(h.IUniverInstanceService),g=t.params;if(!l.isVisible().visible)return;const{unitId:S}=g;if(S===h.DOCS_NORMAL_EDITOR_UNIT_ID_KEY){const{row:a,column:n}=l.getEditLocation(),d={workbook:o,worksheet:s,row:a,column:n,value:h.RichTextValue.create(u.getUnit(h.DOCS_NORMAL_EDITOR_UNIT_ID_KEY).getSnapshot()),isZenEditor:!1};this.fireEvent(this.Event.SheetEditChanging,d)}})),this.registerEventHandler(this.Event.BeforeSheetZoomChange,()=>r.beforeCommandExecuted(t=>{if(t.id!==c.SetZoomRatioCommand.id)return;const i=this.getCommandSheetTarget(t);if(!i)return;const{workbook:o,worksheet:s}=i,l={zoom:t.params.zoomRatio,workbook:o,worksheet:s};if(this.fireEvent(this.Event.BeforeSheetZoomChange,l),l.cancel)throw new h.CanceledError})),this.registerEventHandler(this.Event.SheetZoomChanged,()=>r.onCommandExecuted(t=>{if(t.id!==c.SetZoomRatioCommand.id)return;const i=this.getCommandSheetTarget(t);if(!i)return;const{workbook:o,worksheet:s}=i;this.fireEvent(this.Event.SheetZoomChanged,{zoom:s.getZoom(),workbook:o,worksheet:s})}))}_initObserverListener(e){const r=e.get(v.IRenderManagerService),t=e.get(h.LifecycleService),i=new h.DisposableCollection;this.disposeWithMe(t.lifecycle$.subscribe(u=>{if(u!==h.LifecycleStages.Rendered)return;i.dispose();const g=e.get(c.HoverManagerService),S=e.get(c.DragManagerService);g&&(this.registerEventHandler(this.Event.CellClicked,()=>{var a;return(a=g.currentClickedCell$)==null?void 0:a.pipe(C.filter(n=>!!n)).subscribe(n=>{const d=this.getSheetTarget(n.location.unitId,n.location.subUnitId);d&&this.fireEvent(this.Event.CellClicked,{...d,...n,row:n.location.row,column:n.location.col})})}),this.registerEventHandler(this.Event.CellHover,()=>{var a;return(a=g.currentRichText$)==null?void 0:a.pipe(C.filter(n=>!!n)).subscribe(n=>{const d=this.getSheetTarget(n.unitId,n.subUnitId);d&&this.fireEvent(this.Event.CellHover,{...d,...n,row:n.row,column:n.col})})}),this.registerEventHandler(this.Event.CellPointerDown,()=>{var a;return(a=g.currentPointerDownCell$)==null?void 0:a.pipe(C.filter(n=>!!n)).subscribe(n=>{const d=this.getSheetTarget(n.unitId,n.subUnitId);d&&this.fireEvent(this.Event.CellPointerDown,{...d,...n,row:n.row,column:n.col})})}),this.registerEventHandler(this.Event.CellPointerUp,()=>{var a;return(a=g.currentPointerUpCell$)==null?void 0:a.pipe(C.filter(n=>!!n)).subscribe(n=>{const d=this.getSheetTarget(n.unitId,n.subUnitId);d&&this.fireEvent(this.Event.CellPointerUp,{...d,...n,row:n.row,column:n.col})})}),this.registerEventHandler(this.Event.CellPointerMove,()=>{var a;return(a=g.currentCellPosWithEvent$)==null?void 0:a.pipe(C.filter(n=>!!n)).subscribe(n=>{const d=this.getSheetTarget(n.unitId,n.subUnitId);d&&this.fireEvent(this.Event.CellPointerMove,{...d,...n,row:n.row,column:n.col})})}),this.registerEventHandler(this.Event.DragOver,()=>{var a;return(a=S.currentCell$)==null?void 0:a.pipe(C.filter(n=>!!n)).subscribe(n=>{const d=this.getSheetTarget(n.location.unitId,n.location.subUnitId);d&&this.fireEvent(this.Event.DragOver,{...d,...n,row:n.location.row,column:n.location.col})})}),this.registerEventHandler(this.Event.Drop,()=>{var a;return(a=S.endCell$)==null?void 0:a.pipe(C.filter(n=>!!n)).subscribe(n=>{const d=this.getSheetTarget(n.location.unitId,n.location.subUnitId);d&&this.fireEvent(this.Event.Drop,{...d,...n,row:n.location.row,column:n.location.col})})}),this.registerEventHandler(this.Event.RowHeaderClick,()=>{var a;return(a=g.currentRowHeaderClick$)==null?void 0:a.pipe(C.filter(n=>!!n)).subscribe(n=>{const d=this.getSheetTarget(n.unitId,n.subUnitId);d&&this.fireEvent(this.Event.RowHeaderClick,{...d,row:n.index})})}),this.registerEventHandler(this.Event.RowHeaderPointerDown,()=>{var a;return(a=g.currentRowHeaderPointerDown$)==null?void 0:a.pipe(C.filter(n=>!!n)).subscribe(n=>{const d=this.getSheetTarget(n.unitId,n.subUnitId);d&&this.fireEvent(this.Event.RowHeaderPointerDown,{...d,row:n.index})})}),this.registerEventHandler(this.Event.RowHeaderPointerUp,()=>{var a;return(a=g.currentRowHeaderPointerUp$)==null?void 0:a.pipe(C.filter(n=>!!n)).subscribe(n=>{const d=this.getSheetTarget(n.unitId,n.subUnitId);d&&this.fireEvent(this.Event.RowHeaderPointerUp,{...d,row:n.index})})}),this.registerEventHandler(this.Event.RowHeaderHover,()=>{var a;return(a=g.currentHoveredRowHeader$)==null?void 0:a.pipe(C.filter(n=>!!n)).subscribe(n=>{const d=this.getSheetTarget(n.unitId,n.subUnitId);d&&this.fireEvent(this.Event.RowHeaderHover,{...d,row:n.index})})}),this.registerEventHandler(this.Event.ColumnHeaderClick,()=>{var a;return(a=g.currentColHeaderClick$)==null?void 0:a.pipe(C.filter(n=>!!n)).subscribe(n=>{const d=this.getSheetTarget(n.unitId,n.subUnitId);d&&this.fireEvent(this.Event.ColumnHeaderClick,{...d,column:n.index})})}),this.registerEventHandler(this.Event.ColumnHeaderPointerDown,()=>{var a;return(a=g.currentColHeaderPointerDown$)==null?void 0:a.pipe(C.filter(n=>!!n)).subscribe(n=>{const d=this.getSheetTarget(n.unitId,n.subUnitId);d&&this.fireEvent(this.Event.ColumnHeaderPointerDown,{...d,column:n.index})})}),this.registerEventHandler(this.Event.ColumnHeaderPointerUp,()=>{var a;return(a=g.currentColHeaderPointerUp$)==null?void 0:a.pipe(C.filter(n=>!!n)).subscribe(n=>{const d=this.getSheetTarget(n.unitId,n.subUnitId);d&&this.fireEvent(this.Event.ColumnHeaderPointerUp,{...d,column:n.index})})}),this.registerEventHandler(this.Event.ColumnHeaderHover,()=>{var a;return(a=g.currentHoveredColHeader$)==null?void 0:a.pipe(C.filter(n=>!!n)).subscribe(n=>{const d=this.getSheetTarget(n.unitId,n.subUnitId);d&&this.fireEvent(this.Event.ColumnHeaderHover,{...d,column:n.index})})}))})),this.disposeWithMe(i);const o=new Map;let s;const l=C.combineLatest([r.created$,t.lifecycle$]);this.disposeWithMe(l.subscribe(([u,g])=>{var k;if(u.type===h.UniverInstanceType.UNIVER_SHEET&&(s=u),g<=h.LifecycleStages.Rendered||!s)return;const S=new h.DisposableCollection,a=this.getWorkbook(s.unitId);if(!a)return;o.get(s.unitId)&&((k=o.get(s.unitId))==null||k.dispose()),o.set(s.unitId,S);const n=s.with(c.SheetScrollManagerService),d=s.with(I.SheetsSelectionsService);S.add(this.registerEventHandler(this.Event.Scroll,()=>n.validViewportScrollInfo$.subscribe(E=>{E&&this.fireEvent(this.Event.Scroll,{workbook:a,worksheet:a.getActiveSheet(),...E})}))),S.add(this.registerEventHandler(this.Event.SelectionMoveStart,()=>d.selectionMoveStart$.subscribe(E=>{var b;this.fireEvent(this.Event.SelectionMoveStart,{workbook:a,worksheet:a.getActiveSheet(),selections:(b=E==null?void 0:E.map(f=>f.range))!=null?b:[]})}))),S.add(this.registerEventHandler(this.Event.SelectionMoving,()=>d.selectionMoving$.subscribe(E=>{var b;this.fireEvent(this.Event.SelectionMoving,{workbook:a,worksheet:a.getActiveSheet(),selections:(b=E==null?void 0:E.map(f=>f.range))!=null?b:[]})}))),S.add(this.registerEventHandler(this.Event.SelectionMoveEnd,()=>d.selectionMoveEnd$.subscribe(E=>{var b;this.fireEvent(this.Event.SelectionMoveEnd,{workbook:a,worksheet:a.getActiveSheet(),selections:(b=E==null?void 0:E.map(f=>f.range))!=null?b:[]})}))),S.add(this.registerEventHandler(this.Event.SelectionChanged,()=>d.selectionChanged$.subscribe(E=>{var b;this.fireEvent(this.Event.SelectionChanged,{workbook:a,worksheet:a.getActiveSheet(),selections:(b=E==null?void 0:E.map(f=>f.range))!=null?b:[]})}))),s=null,this.disposeWithMe(S)})),this.disposeWithMe(r.disposed$.subscribe(u=>{var g;(g=o.get(u))==null||g.dispose(),o.delete(u)})),this.disposeWithMe(()=>{o.forEach(u=>{u.dispose()})})}_initialize(e){this._initSheetUIEvent(e),this._initObserverListener(e);const r=e.get(h.ICommandService);this.registerEventHandler(this.Event.BeforeClipboardChange,()=>r.beforeCommandExecuted(t=>{switch(t.id){case m.CopyCommand.id:case m.CutCommand.id:this._beforeClipboardChange();break}})),this.registerEventHandler(this.Event.ClipboardChanged,()=>r.onCommandExecuted(t=>{switch(t.id){case m.CopyCommand.id:case m.CutCommand.id:this._clipboardChanged();break}})),this.registerEventHandler(this.Event.BeforeClipboardPaste,()=>r.beforeCommandExecuted(t=>{switch(t.id){case c.SheetPasteShortKeyCommand.id:this._beforeClipboardPaste(t.params);break;case m.PasteCommand.id:this._beforeClipboardPasteAsync();break}})),this.registerEventHandler(this.Event.ClipboardPasted,()=>r.onCommandExecuted(t=>{switch(t.id){case c.SheetPasteShortKeyCommand.id:this._clipboardPaste(t.params);break;case m.PasteCommand.id:this._clipboardPasteAsync();break}})),this.registerEventHandler(this.Event.SheetSkeletonChanged,()=>r.onCommandExecuted(t=>{if(I.COMMAND_LISTENER_SKELETON_CHANGE.indexOf(t.id)>-1){const i=this.getActiveSheet();if(!i)return;const o=I.getSkeletonChangedEffectedRange(t,i.worksheet.getMaxColumns()).map(s=>{var l,u;return(u=(l=this.getWorkbook(s.unitId))==null?void 0:l.getSheetBySheetId(s.subUnitId))==null?void 0:u.getRange(s.range)}).filter(Boolean);if(!o.length)return;this.fireEvent(this.Event.SheetSkeletonChanged,{workbook:i.workbook,worksheet:i.worksheet,payload:t,skeleton:i.worksheet.getSkeleton(),effectedRanges:o})}}))}_generateClipboardCopyParam(){const e=this.getActiveWorkbook(),r=e==null?void 0:e.getActiveSheet(),t=e==null?void 0:e.getActiveRange();if(!e||!r||!t)return;const o=this._injector.get(c.ISheetClipboardService).generateCopyContent(e.getId(),r.getSheetId(),t.getRange());if(!o)return;const{html:s,plain:l}=o;return{workbook:e,worksheet:r,text:l,html:s,fromSheet:r,fromRange:t}}_beforeClipboardChange(){const e=this._generateClipboardCopyParam();if(e&&(this.fireEvent(this.Event.BeforeClipboardChange,e),e.cancel))throw new h.CanceledError}_clipboardChanged(){const e=this._generateClipboardCopyParam();e&&this.fireEvent(this.Event.ClipboardChanged,e)}_generateClipboardPasteParam(e){if(!e)return;const{htmlContent:r,textContent:t}=e,i=this.getActiveWorkbook(),o=i==null?void 0:i.getActiveSheet();return!i||!o?void 0:{workbook:i,worksheet:o,text:t,html:r}}async _generateClipboardPasteParamAsync(){const e=this.getActiveWorkbook(),r=e==null?void 0:e.getActiveSheet();if(!e||!r)return;const o=(await this._injector.get(m.IClipboardInterfaceService).read())[0];let s;if(o){const l=o.types,u=l.indexOf(m.PLAIN_TEXT_CLIPBOARD_MIME_TYPE)!==-1?await o.getType(m.PLAIN_TEXT_CLIPBOARD_MIME_TYPE).then(S=>S&&S.text()):"",g=l.indexOf(m.HTML_CLIPBOARD_MIME_TYPE)!==-1?await o.getType(m.HTML_CLIPBOARD_MIME_TYPE).then(S=>S&&S.text()):"";s={workbook:e,worksheet:r,text:u,html:g}}return s}_beforeClipboardPaste(e){const r=this._generateClipboardPasteParam(e);if(r&&(this.fireEvent(this.Event.BeforeClipboardPaste,r),r.cancel))throw new h.CanceledError}_clipboardPaste(e){const r=this._generateClipboardPasteParam(e);if(r&&(this.fireEvent(this.Event.ClipboardPasted,r),r.cancel))throw new h.CanceledError}async _beforeClipboardPasteAsync(){if(!m.supportClipboardAPI()){this._injector.get(h.ILogService).warn("[Facade]: The navigator object only supports the browser environment");return}const e=await this._generateClipboardPasteParamAsync();if(e&&(this.fireEvent(this.Event.BeforeClipboardPaste,e),e.cancel))throw new h.CanceledError}async _clipboardPasteAsync(){if(!m.supportClipboardAPI()){this._injector.get(h.ILogService).warn("[Facade]: The navigator object only supports the browser environment");return}const e=await this._generateClipboardPasteParamAsync();if(e&&(this.fireEvent(this.Event.ClipboardPasted,e),e.cancel))throw new h.CanceledError}customizeColumnHeader(e){var g,S;const r=this.getActiveWorkbook();if(!r){console.error("WorkBook not exist");return}const t=r==null?void 0:r.getId(),i=this._injector.get(v.IRenderManagerService),o=r.getActiveSheet(),s=o.getSheetId(),l=i.getRenderById(t);l&&((g=e.headerStyle)!=null&&g.size)&&(l.with(c.SheetSkeletonManagerService).setColumnHeaderSize(l,s,(S=e.headerStyle)==null?void 0:S.size),o==null||o.refreshCanvas()),this._getSheetRenderComponent(t,c.SHEET_VIEW_KEY.COLUMN).setCustomHeader(e),o==null||o.refreshCanvas()}customizeRowHeader(e){const r=this.getActiveWorkbook();if(!r){console.error("WorkBook not exist");return}const t=r==null?void 0:r.getId();this._getSheetRenderComponent(t,c.SHEET_VIEW_KEY.ROW).setCustomHeader(e)}registerSheetRowHeaderExtension(e,...r){const t=this._getSheetRenderComponent(e,c.SHEET_VIEW_KEY.ROW),i=t.register(...r);return h.toDisposable(()=>{i.dispose(),t.makeDirty(!0)})}registerSheetColumnHeaderExtension(e,...r){const t=this._getSheetRenderComponent(e,c.SHEET_VIEW_KEY.COLUMN),i=t.register(...r);return h.toDisposable(()=>{i.dispose(),t.makeDirty(!0)})}registerSheetMainExtension(e,...r){const t=this._getSheetRenderComponent(e,c.SHEET_VIEW_KEY.MAIN),i=t.register(...r);return h.toDisposable(()=>{i.dispose(),t.makeDirty(!0)})}_getSheetRenderComponent(e,r){const i=this._injector.get(v.IRenderManagerService).getRenderById(e);if(!i)throw new Error(`Render Unit with unitId ${e} not found`);const{components:o}=i,s=o.get(r);if(!s)throw new Error("Render component not found");return s}getSheetHooks(){return this._injector.createInstance(p.FSheetHooks)}}M.FUniver.extend(H);class y extends p.FWorkbook{openSiderbar(e){return this._logDeprecation("openSiderbar"),this._injector.get(m.ISidebarService).open(e)}openDialog(e){this._logDeprecation("openDialog");const t=this._injector.get(m.IDialogService).open({...e,onClose:()=>{t.dispose()}});return t}customizeColumnHeader(e){const r=this._workbook.getUnitId();this._getSheetRenderComponent(r,c.SHEET_VIEW_KEY.COLUMN).setCustomHeader(e)}customizeRowHeader(e){const r=this._workbook.getUnitId();this._getSheetRenderComponent(r,c.SHEET_VIEW_KEY.ROW).setCustomHeader(e)}_getSheetRenderComponent(e,r){const i=this._injector.get(v.IRenderManagerService).getRenderById(e);if(!i)throw new Error(`Render Unit with unitId ${e} not found`);const{components:o}=i,s=o.get(r);if(!s)throw new Error("Render component not found");return s}_logDeprecation(e){this._injector.get(h.ILogService).warn("[FWorkbook]",`${e} is deprecated. Please use the function of the same name on "FUniver".`)}generateCellParams(e){const r=this.getActiveSheet();return{row:e.row,column:e.col,workbook:this,worksheet:r}}onCellClick(e){const r=this._injector.get(c.HoverManagerService);return h.toDisposable(r.currentClickedCell$.pipe(C.filter(t=>!!t)).subscribe(t=>{e(t)}))}onCellHover(e){const r=this._injector.get(c.HoverManagerService);return h.toDisposable(r.currentRichText$.pipe(C.filter(t=>!!t)).subscribe(e))}onCellPointerDown(e){const r=this._injector.get(c.HoverManagerService);return h.toDisposable(r.currentPointerDownCell$.subscribe(e))}onCellPointerUp(e){const r=this._injector.get(c.HoverManagerService);return h.toDisposable(r.currentPointerUpCell$.subscribe(e))}onCellPointerMove(e){const r=this._injector.get(c.HoverManagerService);return h.toDisposable(r.currentCellPosWithEvent$.pipe(C.filter(t=>!!t)).subscribe(t=>{e(t,t.event)}))}onDragOver(e){const r=this._injector.get(c.DragManagerService);return h.toDisposable(r.currentCell$.pipe(C.filter(t=>!!t)).subscribe(t=>{e(t)}))}onDrop(e){const r=this._injector.get(c.DragManagerService);return h.toDisposable(r.endCell$.pipe(C.filter(t=>!!t)).subscribe(t=>{e(t)}))}startEditing(){return this._injector.get(h.ICommandService).syncExecuteCommand(c.SetCellEditVisibleOperation.id,{eventType:v.DeviceInputEventType.Dblclick,unitId:this._workbook.getUnitId(),visible:!0})}async endEditing(e){return this._injector.get(h.ICommandService).syncExecuteCommand(c.SetCellEditVisibleOperation.id,{eventType:v.DeviceInputEventType.Keyboard,keycode:e?m.KeyCode.ENTER:m.KeyCode.ESC,visible:!1,unitId:this._workbook.getUnitId()}),await h.awaitTime(0),!0}endEditingAsync(e=!0){return this.endEditing(e)}abortEditingAsync(){return this.endEditingAsync(!1)}getScrollStateBySheetId(e){const r=this._workbook.getUnitId(),i=this._injector.get(v.IRenderManagerService).getRenderById(r);return i?i.with(c.SheetScrollManagerService).getScrollStateByParam({unitId:r,sheetId:e}):null}disableSelection(){const e=this._workbook.getUnitId(),t=this._injector.get(v.IRenderManagerService).getRenderById(e);return t&&t.with(c.ISheetSelectionRenderService).disableSelection(),this}enableSelection(){const e=this._workbook.getUnitId(),t=this._injector.get(v.IRenderManagerService).getRenderById(e);return t&&t.with(c.ISheetSelectionRenderService).enableSelection(),this}transparentSelection(){const e=this._workbook.getUnitId(),t=this._injector.get(v.IRenderManagerService).getRenderById(e);return t&&t.with(c.ISheetSelectionRenderService).transparentSelection(),this}showSelection(){const e=this._workbook.getUnitId(),t=this._injector.get(v.IRenderManagerService).getRenderById(e);return t&&t.with(c.ISheetSelectionRenderService).showSelection(),this}}p.FWorkbook.extend(y);class U extends p.FWorksheet{refreshCanvas(){const e=this._injector.get(v.IRenderManagerService),r=this._fWorkbook.id,t=e.getRenderById(r);if(!t)throw new Error(`Render Unit with unitId ${r} not found`);t.with(c.SheetSkeletonManagerService).reCalculate();const i=t.mainComponent;if(!i)throw new Error("Main component not found");return i.makeDirty(),this}zoom(e){const r=this._injector.get(h.ICommandService),t=Math.min(Math.max(e,.1),4);return r.executeCommand(c.SetZoomRatioCommand.id,{unitId:this._workbook.getUnitId(),subUnitId:this._worksheet.getSheetId(),zoomRatio:t}),this}getZoom(){return this._worksheet.getZoomRatio()}getVisibleRange(){const e=this._workbook.getUnitId(),t=this._injector.get(v.IRenderManagerService).getRenderById(e);let i={startColumn:0,startRow:0,endColumn:0,endRow:0};if(!t)return i;const s=t.with(c.SheetSkeletonManagerService).getCurrentSkeleton();if(!s)return i;const l=s==null?void 0:s.getVisibleRanges();if(!l)return i;i=s.getVisibleRangeByViewport(v.SHEET_VIEWPORT_KEY.VIEW_MAIN);for(const[u,g]of l)v.sheetContentViewportKeys.indexOf(u)!==-1&&(i.startColumn=Math.min(i.startColumn,g.startColumn),i.startRow=Math.min(i.startRow,g.startRow),i.endColumn=Math.max(i.endColumn,g.endColumn),i.endRow=Math.max(i.endRow,g.endRow));return i}scrollToCell(e,r){const t=this._workbook.getUnitId(),o=this._injector.get(v.IRenderManagerService).getRenderById(t);return o&&(o==null?void 0:o.with(c.SheetsScrollRenderController)).scrollToCell(e,r),this}getScrollState(){const e={offsetX:0,offsetY:0,sheetViewStartColumn:0,sheetViewStartRow:0},r=this._workbook.getUnitId(),t=this._worksheet.getSheetId(),o=this._injector.get(v.IRenderManagerService).getRenderById(r);return o&&o.with(c.SheetScrollManagerService).getScrollStateByParam({unitId:r,sheetId:t})||e}onScroll(e){var o;const r=this._workbook.getUnitId(),i=(o=this._injector.get(v.IRenderManagerService).getRenderById(r))==null?void 0:o.with(c.SheetScrollManagerService);if(i){const s=i.validViewportScrollInfo$.subscribe(l=>{e(l)});return h.toDisposable(s)}return h.toDisposable(()=>{})}getSkeleton(){var r,t;const e=(r=this._injector.get(v.IRenderManagerService).getRenderById(this._workbook.getUnitId()))==null?void 0:r.with(c.SheetSkeletonManagerService);return(t=e==null?void 0:e.getWorksheetSkeleton(this._worksheet.getSheetId()))==null?void 0:t.skeleton}autoResizeColumn(e){return this.autoResizeColumns(e,1)}autoResizeColumns(e,r){const t=this._workbook.getUnitId(),i=this._worksheet.getSheetId(),o=[{startColumn:e,endColumn:e+r-1,startRow:0,endRow:this._worksheet.getRowCount()-1}];return this._commandService.syncExecuteCommand(c.SetWorksheetColAutoWidthCommand.id,{unitId:t,subUnitId:i,ranges:o}),this}setColumnAutoWidth(e,r){return this.autoResizeColumns(e,r)}autoResizeRows(e,r){const t=this._workbook.getUnitId(),i=this._worksheet.getSheetId(),o=[{startRow:e,endRow:e+r-1,startColumn:0,endColumn:this._worksheet.getColumnCount()-1}];return this._commandService.syncExecuteCommand(I.SetWorksheetRowIsAutoHeightCommand.id,{unitId:t,subUnitId:i,ranges:o}),this}customizeColumnHeader(e){var l,u;const r=this._workbook.getUnitId(),t=this._worksheet.getSheetId(),o=this._injector.get(v.IRenderManagerService).getRenderById(r);o&&((l=e.headerStyle)!=null&&l.size)&&o.with(c.SheetSkeletonManagerService).setColumnHeaderSize(o,t,(u=e.headerStyle)==null?void 0:u.size),this._getSheetRenderComponent(r,c.SHEET_VIEW_KEY.COLUMN).setCustomHeader(e,t)}customizeRowHeader(e){var l,u;const r=this._workbook.getUnitId(),t=this._worksheet.getSheetId(),o=this._injector.get(v.IRenderManagerService).getRenderById(r);o&&((l=e.headerStyle)!=null&&l.size)&&o.with(c.SheetSkeletonManagerService).setRowHeaderSize(o,t,(u=e.headerStyle)==null?void 0:u.size),this._getSheetRenderComponent(r,c.SHEET_VIEW_KEY.ROW).setCustomHeader(e,t)}setColumnHeaderHeight(e){const r=this._workbook.getUnitId(),t=this._worksheet.getSheetId();return this._commandService.executeCommand(c.SetColumnHeaderHeightCommand.id,{unitId:r,subUnitId:t,size:e}),this}setRowHeaderWidth(e){const r=this._workbook.getUnitId(),t=this._worksheet.getSheetId();return this._commandService.executeCommand(c.SetRowHeaderWidthCommand.id,{unitId:r,subUnitId:t,size:e}),this}_getSheetRenderComponent(e,r){const i=this._injector.get(v.IRenderManagerService).getRenderById(e);if(!i)throw new Error(`Render Unit with unitId ${e} not found`);const{components:o}=i,s=o.get(r);if(!s)throw new Error("Render component not found");return s}}p.FWorksheet.extend(U);class D extends p.FPermission{setPermissionDialogVisible(e){this._permissionService.setShowComponents(e)}}p.FPermission.extend(D);class T extends p.FSheetHooks{onCellPointerMove(e){return h.toDisposable(this._injector.get(c.HoverManagerService).currentPosition$.subscribe(e))}onCellPointerOver(e){return h.toDisposable(this._injector.get(c.HoverManagerService).currentCell$.subscribe(e))}onCellDragOver(e){return h.toDisposable(this._injector.get(c.DragManagerService).currentCell$.subscribe(e))}onCellDrop(e){return h.toDisposable(this._injector.get(c.DragManagerService).endCell$.subscribe(e))}onCellRender(e,r=h.InterceptorEffectEnum.Style,t=I.InterceptCellContentPriority.DATA_VALIDATION){return this._injector.get(I.SheetInterceptorService).intercept(I.INTERCEPTOR_POINT.CELL_CONTENT,{effect:r,handler:(i,o,s)=>(i&&!i.customRender&&e&&(i.customRender=[...e]),s(i)),priority:t})}onBeforeCellEdit(e){return this._injector.get(h.ICommandService).beforeCommandExecuted(r=>{const t=r.params;r.id===c.SetCellEditVisibleOperation.id&&t.visible&&e(t)})}onAfterCellEdit(e){return this._injector.get(h.ICommandService).onCommandExecuted(r=>{const t=r.params;r.id===c.SetCellEditVisibleOperation.id&&!t.visible&&e(t)})}}p.FSheetHooks.extend(T);const _={CellClicked:"CellClicked",CellPointerDown:"CellPointerDown",CellPointerUp:"CellPointerUp",CellPointerMove:"CellPointerMove",CellHover:"CellHover"};class x{get BeforeClipboardChange(){return"BeforeClipboardChange"}get ClipboardChanged(){return"ClipboardChanged"}get BeforeClipboardPaste(){return"BeforeClipboardPaste"}get ClipboardPasted(){return"ClipboardPasted"}get BeforeSheetEditStart(){return"BeforeSheetEditStart"}get SheetEditStarted(){return"SheetEditStarted"}get SheetEditChanging(){return"SheetEditChanging"}get BeforeSheetEditEnd(){return"BeforeSheetEditEnd"}get SheetEditEnded(){return"SheetEditEnded"}get CellClicked(){return _.CellClicked}get CellHover(){return _.CellHover}get CellPointerDown(){return _.CellPointerDown}get CellPointerUp(){return _.CellPointerUp}get CellPointerMove(){return _.CellPointerMove}get DragOver(){return"DragOver"}get Drop(){return"Drop"}get Scroll(){return"Scroll"}get SelectionMoveStart(){return"SelectionMoveStart"}get SelectionChanged(){return"SelectionChanged"}get SelectionMoving(){return"SelectionMoving"}get SelectionMoveEnd(){return"SelectionMoveEnd"}get RowHeaderClick(){return"RowHeaderClick"}get RowHeaderPointerDown(){return"RowHeaderPointerDown"}get RowHeaderPointerUp(){return"RowHeaderPointerUp"}get RowHeaderHover(){return"RowHeaderHover"}get ColumnHeaderClick(){return"ColumnHeaderClick"}get ColumnHeaderPointerDown(){return"ColumnHeaderPointerDown"}get ColumnHeaderPointerUp(){return"ColumnHeaderPointerUp"}get ColumnHeaderHover(){return"ColumnHeaderHover"}get SheetSkeletonChanged(){return"SheetSkeletonChanged"}get BeforeSheetZoomChange(){return"BeforeSheetZoomChange"}get SheetZoomChanged(){return"SheetZoomChanged"}}M.FEventName.extend(x);class B extends p.FRange{getCell(){var l;const e=this._injector.get(v.IRenderManagerService),r=this._injector.get(h.ILogService),t=this._workbook.getUnitId(),i=this._worksheet.getSheetId(),o=e.getRenderById(t),s=(l=o==null?void 0:o.with(c.SheetSkeletonManagerService).getSkeletonParam(i))==null?void 0:l.skeleton;if(!s)throw r.error("[Facade]: `FRange.getCell` can only be called in current worksheet"),new Error("`FRange.getCell` can only be called in current worksheet");return s.getCellWithCoordByIndex(this._range.startRow,this._range.startColumn)}getCellRect(){const{startX:e,startY:r,endX:t,endY:i}=this.getCell(),o={x:e,y:r,width:t-e,height:i-r,top:r,left:e,bottom:i,right:t};return{...o,toJSON:()=>JSON.stringify(o)}}generateHTML(){var t;const r=this._injector.get(c.ISheetClipboardService).generateCopyContent(this._workbook.getUnitId(),this._worksheet.getSheetId(),this._range);return(t=r==null?void 0:r.html)!=null?t:""}attachPopup(e){var s,l,u;e.direction=(s=e.direction)!=null?s:"horizontal",e.extraProps=(l=e.extraProps)!=null?l:{},e.offset=(u=e.offset)!=null?u:[0,0];const{key:r,disposableCollection:t}=R(e,this._injector.get(m.ComponentManager)),o=this._injector.get(c.SheetCanvasPopManagerService).attachPopupToCell(this._range.startRow,this._range.startColumn,{...e,componentKey:r},this.getUnitId(),this._worksheet.getSheetId());return o?(t.add(o),t):(t.dispose(),null)}attachAlertPopup(e){const r=this._injector.get(c.CellAlertManagerService),t={workbook:this._workbook,worksheet:this._worksheet,row:this._range.startRow,col:this._range.startColumn,unitId:this.getUnitId(),subUnitId:this._worksheet.getSheetId()};return r.showAlert({...e,location:t}),{dispose:()=>{r.removeAlert(e.key)}}}attachRangePopup(e){var s,l,u;e.direction=(s=e.direction)!=null?s:"top-center",e.extraProps=(l=e.extraProps)!=null?l:{},e.offset=(u=e.offset)!=null?u:[0,0];const{key:r,disposableCollection:t}=R(e,this._injector.get(m.ComponentManager)),o=this._injector.get(c.SheetCanvasPopManagerService).attachRangePopup(this._range,{...e,componentKey:r},this.getUnitId(),this._worksheet.getSheetId());return o?(t.add(o),t):(t.dispose(),null)}highlight(e,r){const t=this._injector.get(c.IMarkSelectionService),i=t.addShape({range:this._range,style:e,primary:r});if(!i)throw new Error("Failed to highlight current range");return h.toDisposable(()=>{t.removeShape(i)})}showDropdown(e){return this._injector.get(c.ISheetCellDropdownManagerService).showDropdown(e)}}p.FRange.extend(B);function R(w,e){const{componentKey:r,isVue3:t}=w;let i;const o=new h.DisposableCollection;return typeof r=="string"?i=r:(i=`External_${h.generateRandomId(6)}`,o.add(e.register(i,r,{framework:t?"vue3":"react"}))),{key:i,disposableCollection:o}}exports.transformComponentKey=R;
