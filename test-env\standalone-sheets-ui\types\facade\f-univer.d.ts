import { IDisposable, Injector } from '@univerjs/core';
import { IColumnsHeaderCfgParam, IRowsHeaderCfgParam, SheetExtension } from '@univerjs/engine-render';
import { FUniver } from '@univerjs/core/facade';
import { FSheetHooks } from '@univerjs/sheets/facade';
/**
 * @ignore
 */
export interface IFUniverSheetsUIMixin {
    /**
     * @deprecated use same API in FWorkbook and FWorkSheet.
     */
    customizeColumnHeader(cfg: IColumnsHeaderCfgParam): void;
    /**
     * @deprecated use same API in FWorkbook and FWorkSheet.
     */
    customizeRowHeader(cfg: IRowsHeaderCfgParam): void;
    /**
     * Register sheet row header render extensions.
     * @param {string} unitId The unit id of the spreadsheet.
     * @param {SheetExtension[]} extensions The extensions to register.
     * @returns {IDisposable} The disposable instance.
     */
    registerSheetRowHeaderExtension(unitId: string, ...extensions: SheetExtension[]): IDisposable;
    /**
     * Register sheet column header render extensions.
     * @param {string} unitId The unit id of the spreadsheet.
     * @param {SheetExtension[]} extensions The extensions to register.
     * @returns {IDisposable} The disposable instance.
     */
    registerSheetColumnHeaderExtension(unitId: string, ...extensions: SheetExtension[]): IDisposable;
    /**
     * Register sheet main render extensions.
     * @param {string} unitId The unit id of the spreadsheet.
     * @param {SheetExtension[]} extensions The extensions to register.
     * @returns {IDisposable} The disposable instance.
     */
    registerSheetMainExtension(unitId: string, ...extensions: SheetExtension[]): IDisposable;
    /**
     * @deprecated use `univerAPI.addEvent` as instead.
     */
    getSheetHooks(): FSheetHooks;
}
export declare class FUniverSheetsUIMixin extends FUniver implements IFUniverSheetsUIMixin {
    private _initSheetUIEvent;
    private _initObserverListener;
    /**
     * @ignore
     */
    _initialize(injector: Injector): void;
    private _generateClipboardCopyParam;
    private _beforeClipboardChange;
    private _clipboardChanged;
    private _generateClipboardPasteParam;
    private _generateClipboardPasteParamAsync;
    private _beforeClipboardPaste;
    private _clipboardPaste;
    private _beforeClipboardPasteAsync;
    private _clipboardPasteAsync;
    customizeColumnHeader(cfg: IColumnsHeaderCfgParam): void;
    customizeRowHeader(cfg: IRowsHeaderCfgParam): void;
    registerSheetRowHeaderExtension(unitId: string, ...extensions: SheetExtension[]): IDisposable;
    registerSheetColumnHeaderExtension(unitId: string, ...extensions: SheetExtension[]): IDisposable;
    registerSheetMainExtension(unitId: string, ...extensions: SheetExtension[]): IDisposable;
    /**
     * Get sheet render component from render by unitId and view key.
     * @private
     * @param {string} unitId The unit id of the spreadsheet.
     * @param {SHEET_VIEW_KEY} viewKey The view key of the spreadsheet.
     * @returns {Nullable<RenderComponentType>} The render component.
     */
    private _getSheetRenderComponent;
    /**
     * Get sheet hooks.
     * @returns {FSheetHooks} FSheetHooks instance
     */
    getSheetHooks(): FSheetHooks;
}
declare module '@univerjs/core/facade' {
    interface FUniver extends IFUniverSheetsUIMixin {
    }
}
