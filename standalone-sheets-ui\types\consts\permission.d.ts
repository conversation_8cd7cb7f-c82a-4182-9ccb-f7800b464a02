import { UnitAction } from '@univerjs/protocol';
export declare const UNIVER_SHEET_PERMISSION_PLUGIN_NAME = "UNIVER_SHEET_PERMISSION_PLUGIN";
export declare const UNIVER_SHEET_PERMISSION_PANEL = "UNIVER_SHEET_PERMISSION_PANEL";
export declare const UNIVER_SHEET_PERMISSION_USER_PART = "UNIVER_SHEET_PERMISSION_USER_PART";
export declare const UNIVER_SHEET_PERMISSION_PANEL_FOOTER = "UNIVER_SHEET_PERMISSION_PANEL_FOOTER";
export declare const UNIVER_SHEET_PERMISSION_USER_DIALOG = "UNIVER_SHEET_PERMISSION_USER_DIALOG";
export declare const UNIVER_SHEET_PERMISSION_DIALOG = "UNIVER_SHEET_PERMISSION_DIALOG";
export declare const UNIVER_SHEET_PERMISSION_USER_DIALOG_ID = "UNIVER_SHEET_PERMISSION_USER_DIALOG_ID";
export declare const UNIVER_SHEET_PERMISSION_DIALOG_ID = "UNIVER_SHEET_PERMISSION_DIALOG_ID";
export declare const subUnitPermissionTypeMap: Partial<Record<UnitAction, string>>;
export declare const defaultWorksheetUnitActionList: UnitAction[];
export declare const permissionMenuIconKey = "sheet-permission-menu-icon";
export declare const permissionDeleteIconKey = "sheet-permission-delete-icon";
export declare const permissionEditIconKey = "sheet-permission-edit-icon";
export declare const permissionCheckIconKey = "sheet-permission-check-icon";
export declare const permissionLockIconKey = "sheet-permission-lock-icon";
