import { IAccessor } from '@univerjs/core';
import { IMenuButtonItem, IMenuSelectorItem } from '@univerjs/ui';
export declare function CellMergeMenuItemFactory(accessor: IAccessor): IMenuSelectorItem<string>;
export declare function CellMergeAllMenuItemFactory(accessor: IAccessor): IMenuButtonItem<string>;
export declare function CellMergeVerticalMenuItemFactory(accessor: IAccessor): IMenuButtonItem<string>;
export declare function CellMergeHorizontalMenuItemFactory(accessor: IAccessor): IMenuButtonItem<string>;
export declare function CellMergeCancelMenuItemFactory(accessor: IAccessor): IMenuButtonItem<string>;
