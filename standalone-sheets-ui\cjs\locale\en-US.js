"use strict";const e={spreadsheetLabel:"Spreadsheet",spreadsheetRightLabel:"more Sheets",toolbar:{undo:"Undo",redo:"Redo",formatPainter:"Paint format",font:"Font",fontSize:"Font size",bold:"Bold",italic:"Italic",strikethrough:"Strikethrough",subscript:"Subscript",superscript:"Superscript",underline:"Underline",textColor:{main:"Text color",right:"Choose color"},resetColor:"Reset",fillColor:{main:"Fill color",right:"Choose color"},border:{main:"Border",right:"Border style"},mergeCell:{main:"Merge cells",right:"Choose merge type"},horizontalAlignMode:{main:"Horizontal align",right:"Alignment"},verticalAlignMode:{main:"Vertical align",right:"Alignment"},textWrapMode:{main:"Text wrap",right:"Text wrap mode"},textRotateMode:{main:"Text rotate",right:"Text rotate mode"},more:"More",toggleGridlines:"Toggle Gridlines"},align:{left:"left",center:"center",right:"right",top:"top",middle:"middle",bottom:"bottom"},button:{confirm:"OK",cancel:"Cancel",close:"Close",update:"Update",delete:"Delete",insert:"Insert",prevPage:"Previous",nextPage:"Next",total:"total:"},punctuation:{tab:"Tab",semicolon:"semicolond",comma:"comma",space:"space"},colorPicker:{collapse:"Collapse",customColor:"CUSTOM",change:"Change",confirmColor:"OK",cancelColor:"Cancel"},borderLine:{borderTop:"borderTop",borderBottom:"borderBottom",borderLeft:"borderLeft",borderRight:"borderRight",borderNone:"borderNone",borderAll:"borderAll",borderOutside:"borderOutside",borderInside:"borderInside",borderHorizontal:"borderHorizontal",borderVertical:"borderVertical",borderColor:"borderColor",borderSize:"borderSize",borderType:"borderType"},merge:{all:"Merge all",vertical:"Vertical merge",horizontal:"Horizontal merge",cancel:"Cancel merge",overlappingError:"Cannot merge overlapping areas",partiallyError:"Cannot perform this operation on partially merged cells",confirm:{title:"Continue merging would only keep the upper-left cell value, discard other values. Are you sure to continue?",cancel:"Cancel merging",confirm:"Continue merging",warning:"Warning",dismantleMergeCellWarning:"This will cause some merged cells to be split. Do you want to continue?"}},filter:{confirm:{error:"There was a problem",notAllowedToInsertRange:"Not allowed to move cells here until filter is cleared"}},textWrap:{overflow:"Overflow",wrap:"Wrap",clip:"Clip"},textRotate:{none:"None",angleUp:"Tilt Up",angleDown:"Tilt Down",vertical:"Stack Vertically",rotationUp:"Rotate Up",rotationDown:"Rotate Down"},sheetConfig:{delete:"Delete",copy:"Copy",rename:"Rename",changeColor:"Change color",hide:"Hide",unhide:"Unhide",moveLeft:"Move left",moveRight:"Move right",resetColor:"Reset color",cancelText:"Cancel",chooseText:"Confirm color",tipNameRepeat:"The name of the tab page cannot be repeated! Please revise",noMoreSheet:"The workbook contains at least one visual worksheet. To delete the selected worksheet, please insert a new worksheet or show a hidden worksheet.",confirmDelete:"Are you sure to delete",redoDelete:"Can be undo by Ctrl+Z",noHide:"Can't hide, at least keep one sheet tag",chartEditNoOpt:"This operation is not allowed in chart editing mode!",sheetNameErrorTitle:"There was a problem",sheetNameSpecCharError:"The name cannot exceed 31 characters, cannot start or end with ', and cannot contain: [ ] : \\ ? * /",sheetNameCannotIsEmptyError:"The sheet name cannot be empty.",sheetNameAlreadyExistsError:"The sheet name already exists. Please enter another name.",deleteSheet:"Delete worksheet",deleteSheetContent:"Confirm to delete this worksheet. It will not be retrieved after deletion. Are you sure you want to delete it?",addProtectSheet:"Protect Worksheet",removeProtectSheet:"Unprotect Worksheet",changeSheetPermission:"Change Worksheet Permissions",viewAllProtectArea:"View All Protection Ranges"},rightClick:{copy:"Copy",cut:"Cut",paste:"Paste",pasteSpecial:"Paste Special",pasteValue:"Paste Value",pasteFormat:"Paste Format",pasteColWidth:"Paste Column Width",pasteBesidesBorder:"Paste Besides Border Styles",insert:"Insert",insertRow:"Insert Row",insertRowBefore:"Insert Row Before",insertRowsAfter:"Insert",insertRowsAbove:"Insert",insertRowsAfterSuffix:"rows after",insertRowsAboveSuffix:"rows above",insertColumn:"Insert Column",insertColumnBefore:"Insert Column Before",insertColsLeft:"Insert",insertColsRight:"Insert",insertColsLeftSuffix:"cols left",insertColsRightSuffix:"cols right",delete:"Delete",deleteCell:"Delete Cell",insertCell:"Insert Cell",deleteSelected:"Delete Selected ",hide:"Hide",hideSelected:"Hide Selected ",showHide:"Show Hidden",toTopAdd:"Towards Top Add",toBottomAdd:"Towards Bottom Add",toLeftAdd:"Towards Left Add",toRightAdd:"Towards Right Add",deleteSelectedRow:"Delete Selected Row",deleteSelectedColumn:"Delete Selected Column",hideSelectedRow:"Hide Selected Row",showHideRow:"Show Hide Row",rowHeight:"Row Height",hideSelectedColumn:"Hide Selected Column",showHideColumn:"Show Hide Column",columnWidth:"Column Width",moveLeft:"Move Left",moveUp:"Move Up",moveRight:"Move Right",moveDown:"Move Down",add:"Add",row:"Row",column:"Column",confirm:"Confirm",clearSelection:"Clear",clearContent:"Clear Contents",clearFormat:"Clear Formats",clearAll:"Clear All",root:"Root",log:"Log",delete0:"Delete 0 values at both ends",removeDuplicate:"Remove duplicate values",byRow:"By row",byCol:"By column",generateNewMatrix:"Generate new matrix",fitContent:"Fit for data",freeze:"Freeze",freezeCol:"Freeze to this column",freezeRow:"Freeze to this row",cancelFreeze:"Cancel freeze",deleteAllRowsAlert:"You can't delete all the rows on the sheet",deleteAllColumnsAlert:"You can't delete all the columns on the sheet",hideAllRowsAlert:"You can't hide all the rows on the sheet",hideAllColumnsAlert:"You can't hide all the columns on the sheet",protectRange:"Protect Rows And Columns",editProtectRange:"Set Protection Range",removeProtectRange:"Remove Protection Range",turnOnProtectRange:"Add Protection Range",viewAllProtectArea:"View All Protection Ranges"},info:{tooltip:"Tooltip",error:"Error",notChangeMerge:"You cannot make partial changes to the merged cells",detailUpdate:"New opened",detailSave:"Local cache restored",row:"",column:"",loading:"Loading...",copy:"Copy",return:"Exit",rename:"Rename",tips:"Rename",noName:"Untitled spreadsheet",wait:"waiting for update",add:"Add",addLast:"more rows at bottom",backTop:"Back to the top",pageInfo:"Total ${total}, ${totalPage} page, current ${currentPage}",nextPage:"Next",tipInputNumber:"Please enter the number",tipInputNumberLimit:"The increase range is limited to 1-100",tipRowHeightLimit:"Row height must be between 0 ~ 545",tipColumnWidthLimit:"The column width must be between 0 ~ 2038",pageInfoFull:"Total ${total}, ${totalPage} page, All data displayed",problem:"There was a problem",forceStringInfo:"Number stored as text"},clipboard:{paste:{exceedMaxCells:"The number of cells pasted exceeds the maximum number of cells",overlappingMergedCells:"The paste area overlaps with merged cells"},shortCutNotify:{title:"Kindly paste using keyboard shortcuts.",useShortCutInstead:"Detected Excel content. Use keyboard shortcut to paste."}},statusbar:{sum:"Sum",average:"Average",min:"Min",max:"Max",count:"Numerical Count",countA:"Count",clickToCopy:"Click to Copy",copied:"Copied"},autoFill:{copy:"Copy Cell",series:"Fill Series",formatOnly:"Format Only",noFormat:"No Format"},rangeSelector:{placeholder:"Select range or input value",tooltip:"Select range"},shortcut:{sheet:{"zoom-in":"Zoom in","zoom-out":"Zoom out","reset-zoom":"Reset zoom level","select-below-cell":"Select the cell below","select-up-cell":"Select the cell above","select-left-cell":"Select the left cell","select-right-cell":"Select the right cell","select-next-cell":"Select the next cell","select-previous-cell":"Select the previous cell","select-up-value-cell":"Select the cell above that has value","select-below-value-cell":"Select the cell below that has value","select-left-value-cell":"Select the cell left that has value","select-right-value-cell":"Select the cell right that has value","expand-selection-down":"Expand selection down","expand-selection-up":"Expand selection up","expand-selection-left":"Expand selection left","expand-selection-right":"Expand selection right","expand-selection-to-left-gap":"Expand selection to the left gap","expand-selection-to-below-gap":"Expand selection to the below gap","expand-selection-to-right-gap":"Expand selection to the right gap","expand-selection-to-up-gap":"Expand selection to the up gap","select-all":"Select all","toggle-editing":"Toggle editing","delete-and-start-editing":"Clear and start editing","abort-editing":"Abort editing","break-line":"Break line","set-bold":"Toggle bold","start-editing":"Start Editing (Selection into the Editor)","set-italic":"Toggle italic","set-underline":"Toggle underline","set-strike-through":"Toggle strike through"}},"sheet-view":"Sheet View","sheet-edit":"Sheet Edit",definedName:{managerTitle:"Manager named",managerDescription:"Create a defined name by selecting cells or formulas, and entering the desired name into the text box.",addButton:"Add a defined name",featureTitle:"Defined names",ratioRange:"Range",ratioFormula:"Formula",confirm:"Confirm",cancel:"Cancel",scopeWorkbook:"Workbook",inputNamePlaceholder:"Please enter a name(No space allowed)",inputCommentPlaceholder:"Please enter a comment",inputRangePlaceholder:"Please input range(No space allowed)",inputFormulaPlaceholder:"Please input a formula(No space allowed)",nameEmpty:"Name cannot be empty",nameDuplicate:"Name already exists",formulaOrRefStringEmpty:"Formula or reference string cannot be empty",formulaOrRefStringInvalid:"Invalid formula or reference string",defaultName:"DefinedName",updateButton:"Update",deleteButton:"Delete",deleteConfirmText:"Are you sure you want to delete this defined name?",nameConflict:"The name conflicts with the function name",nameInvalid:"The name is invalid",nameSheetConflict:"The name conflicts with the sheet name"},uploadLoading:{loading:"Loading..., remaining",error:"Error"},permission:{toolbarMenu:"Protection",panel:{title:"Protect Rows and Columns",name:"Name",protectedRange:"Protected Range",permissionDirection:"Permission Description",permissionDirectionPlaceholder:"Enter permission description",editPermission:"Edit Permissions",onlyICanEdit:"Only I can edit",designedUserCanEdit:"Specified users can edit",viewPermission:"View Permissions",othersCanView:"Others can view",noOneElseCanView:"No one else can view",designedPerson:"Specified persons",addPerson:"Add person",canEdit:"Can edit",canView:"Can view",delete:"Delete",currentSheet:"Current sheet",allSheet:"All sheets",edit:"Edit",Print:"Print",Comment:"Comment",Copy:"Copy",SetCellStyle:"Set cell style",SetCellValue:"Set cell value",SetHyperLink:"Set hyperlink",Sort:"Sort",Filter:"Filter",PivotTable:"Pivot table",FloatImage:"Float image",RowHeightColWidth:"Row height and column width",RowHeightColWidthReadonly:"Read-only row height and column width",FilterReadonly:"Read-only filter",nameError:"Name cannot be empty",created:"Created",iCanEdit:"I can edit",iCanNotEdit:"I can't edit",iCanView:"I can view",iCanNotView:"I can't view",emptyRangeError:"Range cannot be empty",rangeOverlapError:"Range cannot overlap",rangeOverlapOverPermissionError:"Range cannot overlap with the range that has the same permission",InsertHyperlink:"Insert hyperlink",SetRowStyle:"Set row style",SetColumnStyle:"Set column style",InsertColumn:"Insert column",InsertRow:"Insert row",DeleteRow:"Delete row",DeleteColumn:"Delete column",EditExtraObject:"Edit extra object"},dialog:{allowUserToEdit:"Allow user to edit",allowedPermissionType:"Allowed permission types",setCellValue:"Set cell value",setCellStyle:"Set cell style",copy:"Copy",alert:"Alert",search:"Search",alertContent:"This range has been protected and no editing permissions are currently available. If you need to edit, please contact the creator.",userEmpty:"no designated person , Share link to invite specific people.",listEmpty:"You haven't set up any ranges or sheets as protected.",commonErr:"The range is protected, and you do not have permission for this operation. To edit, please contact the creator.",editErr:"The range is protected, and you do not have edit permission. To edit, please contact the creator.",pasteErr:"The range is protected, and you do not have paste permission. To paste, please contact the creator.",setStyleErr:"The range is protected, and you do not have permission to set styles. To set styles, please contact the creator.",copyErr:"The range is protected, and you do not have copy permission. To copy, please contact the creator.",workbookCopyErr:"The workbook is protected, and you do not have permission to copy. To copy, please contact the creator.",setRowColStyleErr:"The range is protected, and you do not have permission to set row and column styles. To set row and column styles, please contact the creator.",moveRowColErr:"The range is protected, and you do not have permission to move rows and columns. To move rows and columns, please contact the creator.",moveRangeErr:"The range is protected, and you do not have permission to move the selection. To move the selection, please contact the creator.",autoFillErr:"The range is protected, and you do not have permission for auto-fill. To use auto-fill, please contact the creator.",filterErr:"The range is protected, and you do not have filtering permission. To filter, please contact the creator.",operatorSheetErr:"The worksheet is protected, and you do not have permission to operate the worksheet. To operate the worksheet, please contact the creator.",insertOrDeleteMoveRangeErr:"The inserted or deleted range intersects with the protected range, and this operation is not supported for now.",printErr:"The worksheet is protected, and you do not have permission to print. To print, please contact the creator.",formulaErr:"The range or the referenced range is protected, and you do not have edit permission. To edit, please contact the creator.",hyperLinkErr:"The range is protected, and you do not have permission to set hyperlinks. To set hyperlinks, please contact the creator."},button:{confirm:"Confirm",cancel:"Cancel",addNewPermission:"Add new permission"}}};module.exports=e;
