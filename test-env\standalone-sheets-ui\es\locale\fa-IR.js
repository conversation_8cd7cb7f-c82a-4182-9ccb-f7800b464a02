const e = {
  spreadsheetLabel: "برگه گسترده",
  spreadsheetRightLabel: "برگه‌های بیشتر",
  toolbar: {
    undo: "بازگرداندن",
    redo: "تکرار",
    formatPainter: "کپی قالب",
    font: "فونت",
    fontSize: "اندازه فونت",
    bold: "پررنگ",
    italic: "ایتالیک",
    strikethrough: "خط‌دار",
    subscript: "زیرنویس",
    superscript: "بالانویس",
    underline: "خط‌کشی",
    textColor: {
      main: "رنگ متن",
      right: "انتخاب رنگ"
    },
    resetColor: "بازنشانی",
    fillColor: {
      main: "رنگ پر کردن",
      right: "انتخاب رنگ"
    },
    border: {
      main: "حاشیه",
      right: "سبک حاشیه"
    },
    mergeCell: {
      main: "ادغام سلول‌ها",
      right: "انتخاب نوع ادغام"
    },
    horizontalAlignMode: {
      main: "تراز افقی",
      right: "تراز"
    },
    verticalAlignMode: {
      main: "تراز عمودی",
      right: "تراز"
    },
    textWrapMode: {
      main: "پیچش متن",
      right: "حالت پیچش متن"
    },
    textRotateMode: {
      main: "چرخش متن",
      right: "حالت چرخش متن"
    },
    more: "بیشتر",
    toggleGridlines: "تغییر وضعیت خطوط شبکه"
  },
  align: {
    left: "چپ",
    center: "وسط",
    right: "راست",
    top: "بالا",
    middle: "وسط",
    bottom: "پایین"
  },
  button: {
    confirm: "تایید",
    cancel: "انصراف",
    close: "بستن",
    update: "به‌روزرسانی",
    delete: "حذف",
    insert: "درج",
    prevPage: "قبلی",
    nextPage: "بعدی",
    total: "مجموع:"
  },
  punctuation: {
    tab: "تب",
    semicolon: "نقطه‌ویرگول",
    comma: "ویرگول",
    space: "فاصله"
  },
  colorPicker: {
    collapse: "جمع کردن",
    customColor: "سفارشی",
    change: "تغییر",
    confirmColor: "تایید",
    cancelColor: "انصراف"
  },
  borderLine: {
    borderTop: "خط بالای قاب",
    borderBottom: "خط پایینی قاب",
    borderLeft: "خط چپ قاب",
    borderRight: "خط راست قاب",
    borderNone: "بدون حاشیه",
    borderAll: "همه حاشیه‌ها",
    borderOutside: "حاشیه بیرونی",
    borderInside: "حاشیه داخلی",
    borderHorizontal: "حاشیه افقی",
    borderVertical: "حاشیه عمودی",
    borderColor: "رنگ حاشیه",
    borderSize: "اندازه حاشیه",
    borderType: "نوع حاشیه"
  },
  merge: {
    all: "ادغام همه",
    vertical: "ادغام عمودی",
    horizontal: "ادغام افقی",
    cancel: "لغو ادغام",
    overlappingError: "نمی‌توان نواحی هم‌پوشان را ادغام کرد",
    partiallyError: "نمی‌توان این عملیات را روی سلول‌های ادغام‌شده جزئی انجام داد",
    confirm: {
      title: "ادامه ادغام فقط مقدار سلول بالا-چپ را نگه می‌دارد و مقادیر دیگر حذف خواهند شد. آیا مطمئن هستید که می‌خواهید ادامه دهید؟",
      cancel: "لغو ادغام",
      confirm: "ادامه ادغام",
      warning: "هشدار",
      dismantleMergeCellWarning: "این باعث شکستن برخی از سلول‌های ادغام‌شده می‌شود. آیا می‌خواهید ادامه دهید؟"
    }
  },
  filter: {
    confirm: {
      error: "مشکلی پیش آمد",
      notAllowedToInsertRange: "تا زمانی که فیلتر پاک نشود، اجازه حرکت سلول‌ها به اینجا داده نمی‌شود"
    }
  },
  textWrap: {
    overflow: "سرریز",
    wrap: "بپیچ",
    clip: "برش"
  },
  textRotate: {
    none: "هیچ",
    angleUp: "شیب بالا",
    angleDown: "شیب پایین",
    vertical: "عمودی انباشته کردن",
    rotationUp: "چرخش بالا",
    rotationDown: "چرخش پایین"
  },
  sheetConfig: {
    delete: "حذف",
    copy: "کپی",
    rename: "تغییر نام",
    changeColor: "تغییر رنگ",
    hide: "پنهان کردن",
    unhide: "نشان دادن",
    moveLeft: "جابجایی به چپ",
    moveRight: "جابجایی به راست",
    resetColor: "بازنشانی رنگ",
    cancelText: "انصراف",
    chooseText: "تایید رنگ",
    tipNameRepeat: "نام برگه زبانه نمی‌تواند تکرار شود! لطفا تجدید نظر کنید",
    noMoreSheet: "کاربرگ حداقل شامل یک برگه بصری است. برای حذف برگه انتخاب شده، لطفا یک برگه جدید وارد کنید یا یک برگه پنهان را نمایش دهید.",
    confirmDelete: "آیا مطمئن هستید که می‌خواهید حذف کنید",
    redoDelete: "می‌تواند با Ctrl+Z لغو شود",
    noHide: "نمی‌توان پنهان کرد، حداقل یک برگه تگ را نگه دارید",
    chartEditNoOpt: "این عملیات در حالت ویرایش نمودار مجاز نیست!",
    sheetNameErrorTitle: "مشکلی پیش آمد",
    sheetNameSpecCharError: "نام نمی‌تواند از 31 کاراکتر بیشتر باشد، نمی‌تواند با ' شروع یا پایان یابد و نمی‌تواند شامل: [ ] : \\ ? * / باشد",
    sheetNameCannotIsEmptyError: "نام برگه نمی‌تواند خالی باشد.",
    sheetNameAlreadyExistsError: "نام برگه قبلاً وجود دارد. لطفا نام دیگری وارد کنید.",
    deleteSheet: "حذف برگه",
    deleteSheetContent: "تایید حذف این برگه. پس از حذف، بازیابی نخواهد شد. آیا مطمئن هستید که می‌خواهید آن را حذف کنید؟",
    addProtectSheet: "محافظت از برگه",
    removeProtectSheet: "لغو محافظت از برگه",
    changeSheetPermission: "تغییر مجوزهای برگه",
    viewAllProtectArea: "مشاهده همه محدوده‌های محافظت شده"
  },
  rightClick: {
    copy: "کپی",
    cut: "برش",
    paste: "چسباندن",
    pasteSpecial: "چسباندن ویژه",
    pasteValue: "چسباندن مقدار",
    pasteFormat: "چسباندن قالب",
    pasteColWidth: "چسباندن عرض ستون",
    pasteBesidesBorder: "چسباندن کنار سبک‌های مرز",
    insert: "درج",
    insertRow: "درج سطر",
    insertRowBefore: "درج سطر قبل از",
    insertRowsAfter: "صفوف بعد ذلك",
    insertRowsAbove: "صفوف بعد ذلك",
    insertRowsAfterSuffix: "أدخل",
    insertRowsAboveSuffix: "أدخل",
    insertColumn: "درج ستون",
    insertColumnBefore: "درج ستون قبل از",
    insertColsLeft: "أعمدة إلى اليسار",
    insertColsRight: "صفوف إلى اليمين",
    insertColsLeftSuffix: "أدخل",
    insertColsRightSuffix: "أدخل",
    delete: "حذف",
    deleteCell: "حذف سلول",
    insertCell: "درج سلول",
    deleteSelected: "حذف انتخاب شده",
    hide: "پنهان کردن",
    hideSelected: "پنهان کردن انتخاب شده",
    showHide: "نمایش پنهان شده",
    toTopAdd: "اضافه کردن به بالا",
    toBottomAdd: "اضافه کردن به پایین",
    toLeftAdd: "اضافه کردن به چپ",
    toRightAdd: "اضافه کردن به راست",
    deleteSelectedRow: "حذف سطر انتخاب شده",
    deleteSelectedColumn: "حذف ستون انتخاب شده",
    hideSelectedRow: "پنهان کردن سطر انتخاب شده",
    showHideRow: "نمایش سطر انتخاب شده",
    rowHeight: "ارتفاع سطر",
    hideSelectedColumn: "پنهان کردن ستون انتخاب شده",
    showHideColumn: "نمایش پنهان کردن ستون",
    columnWidth: "عرض ستون",
    moveLeft: "جابجایی به چپ",
    moveUp: "جابجایی به بالا",
    moveRight: "جابجایی به راست",
    moveDown: "جابجایی به پایین",
    add: "اضافه کردن",
    row: "سطر",
    column: "ستون",
    confirm: "تایید",
    clearSelection: "پاک کردن",
    clearContent: "پاک کردن محتوا",
    clearFormat: "پاک کردن قالب‌ها",
    clearAll: "پاک کردن همه",
    root: "ریشه",
    log: "لوگاریتم",
    delete0: "حذف مقادیر 0 در دو طرف",
    removeDuplicate: "حذف مقادیر تکراری",
    byRow: "بر اساس سطر",
    byCol: "بر اساس ستون",
    generateNewMatrix: "ایجاد ماتریس جدید",
    fitContent: "مناسب برای داده‌ها",
    freeze: "انجماد",
    freezeCol: "انجماد در این ستون",
    freezeRow: "انجماد در این سطر",
    cancelFreeze: "لغو انجماد",
    deleteAllRowsAlert: "شما نمی‌توانید تمام سطرهای برگه را حذف کنید",
    deleteAllColumnsAlert: "شما نمی‌توانید تمام ستون‌های برگه را حذف کنید",
    hideAllRowsAlert: "شما نمی‌توانید تمام سطرهای برگه را پنهان کنید",
    hideAllColumnsAlert: "شما نمی‌توانید تمام ستون‌های برگه را پنهان کنید",
    protectRange: "محافظت از سطرها و ستون‌ها",
    editProtectRange: "تنظیم محدوده محافظت",
    removeProtectRange: "حذف محدوده محافظت",
    turnOnProtectRange: "اضافه کردن محدوده محافظت",
    viewAllProtectArea: "مشاهده همه محدوده‌های محافظت شده"
  },
  info: {
    tooltip: "راهنما",
    error: "خطا",
    notChangeMerge: "شما نمی‌توانید تغییرات جزئی در سلول‌های ادغام شده ایجاد کنید",
    detailUpdate: "جدید باز شد",
    detailSave: "پنهانگاه محلی بازیابی شد",
    row: "",
    column: "",
    loading: "در حال بارگیری...",
    copy: "کپی",
    return: "خروج",
    rename: "تغییر نام",
    tips: "تغییر نام",
    noName: "بدون عنوان صفحه گسترده",
    wait: "در انتظار به‌روزرسانی",
    add: "اضافه کردن",
    addLast: "سطرهای بیشتر در پایین",
    backTop: "بازگشت به بالا",
    // eslint-disable-next-line no-template-curly-in-string
    pageInfo: "مجموع ${total}، ${totalPage} صفحه، جاری ${currentPage}",
    nextPage: "بعدی",
    tipInputNumber: "لطفا عدد را وارد کنید",
    tipInputNumberLimit: "محدوده افزایش محدود به 1-100 است",
    tipRowHeightLimit: "ارتفاع سطر باید بین 0 ~ 545 باشد",
    tipColumnWidthLimit: "عرض ستون باید بین 0 ~ 2038 باشد",
    // eslint-disable-next-line no-template-curly-in-string
    pageInfoFull: "مجموع ${total}، ${totalPage} صفحه، همه داده‌ها نمایش داده شده",
    problem: "مشکلی پیش آمد",
    forceStringInfo: "عدد به عنوان متن ذخیره شده"
  },
  clipboard: {
    paste: {
      exceedMaxCells: "تعداد سلول‌های چسبانده شده از حداکثر تعداد سلول‌ها فراتر می‌رود",
      overlappingMergedCells: "منطقه چسباندن با سلول‌های ادغام شده همپوشانی دارد"
    },
    shortCutNotify: {
      title: "لطفا با استفاده از میانبرهای صفحه کلید بچسبانید.",
      useShortCutInstead: "محتوای اکسل تشخیص داده شد. از میانبر صفحه کلید برای چسباندن استفاده کنید."
    }
  },
  statusbar: {
    sum: "جمع",
    average: "میانگین",
    min: "حداقل",
    max: "حداکثر",
    count: "شمارش عددی",
    countA: "شمارش",
    clickToCopy: "کلیک کنید تا کپی کنید",
    copied: "کپی شد"
  },
  autoFill: {
    copy: "کپی سلول",
    series: "پر کردن سری",
    formatOnly: "فقط قالب",
    noFormat: "بدون قالب"
  },
  rangeSelector: {
    placeholder: "انتخاب محدوده یا وارد کردن مقدار",
    tooltip: "انتخاب محدوده"
  },
  shortcut: {
    sheet: {
      "zoom-in": "بزرگنمایی",
      "zoom-out": "کوچکنمایی",
      "reset-zoom": "بازنشانی سطح بزرگنمایی",
      "select-below-cell": "انتخاب سلول پایین",
      "select-up-cell": "انتخاب سلول بالا",
      "select-left-cell": "انتخاب سلول چپ",
      "select-right-cell": "انتخاب سلول راست",
      "select-next-cell": "انتخاب سلول بعدی",
      "select-previous-cell": "انتخاب سلول قبلی",
      "select-up-value-cell": "انتخاب سلول بالا که دارای مقدار است",
      "select-below-value-cell": "انتخاب سلول پایین که دارای مقدار است",
      "select-left-value-cell": "انتخاب سلول چپ که دارای مقدار است",
      "select-right-value-cell": "انتخاب سلول راست که دارای مقدار است",
      "expand-selection-down": "گسترش انتخاب به پایین",
      "expand-selection-up": "گسترش انتخاب به بالا",
      "expand-selection-left": "گسترش انتخاب به چپ",
      "expand-selection-right": "گسترش انتخاب به راست",
      "expand-selection-to-left-gap": "گسترش انتخاب به شکاف چپ",
      "expand-selection-to-below-gap": "گسترش انتخاب به شکاف پایین",
      "expand-selection-to-right-gap": "گسترش انتخاب به شکاف راست",
      "expand-selection-to-up-gap": "گسترش انتخاب به شکاف بالا",
      "select-all": "انتخاب همه",
      "toggle-editing": "تغییر حالت ویرایش",
      "delete-and-start-editing": "پاک کردن و شروع ویرایش",
      "abort-editing": "لغو ویرایش",
      "break-line": "شکستن خط",
      "set-bold": "تغییر حالت پررنگ",
      "start-editing": "شروع ویرایش (انتخاب در ویرایشگر)",
      "set-italic": "تغییر حالت مورب",
      "set-underline": "تغییر حالت خط زیرین",
      "set-strike-through": "تغییر حالت خط‌خورده"
    }
  },
  "sheet-view": "نمای برگه",
  "sheet-edit": "ویرایش برگه",
  definedName: {
    managerTitle: "مدیر نامگذاری شده",
    managerDescription: "با انتخاب سلول‌ها یا فرمول‌ها و وارد کردن نام دلخواه در کادر متن، یک نام تعریف شده ایجاد کنید.",
    addButton: "اضافه کردن نام تعریف شده",
    featureTitle: "نام‌های تعریف شده",
    ratioRange: "محدوده",
    ratioFormula: "فرمول",
    confirm: "تایید",
    cancel: "انصراف",
    scopeWorkbook: "کاربرگ",
    inputNamePlaceholder: "لطفا نام را وارد کنید (فاصله مجاز نیست)",
    inputCommentPlaceholder: "لطفا یک نظر وارد کنید",
    inputRangePlaceholder: "لطفا محدوده را وارد کنید (فاصله مجاز نیست)",
    inputFormulaPlaceholder: "لطفا یک فرمول وارد کنید (فاصله مجاز نیست)",
    nameEmpty: "نام نمی‌تواند خالی باشد",
    nameDuplicate: "نام قبلاً وجود دارد",
    formulaOrRefStringEmpty: "فرمول یا رشته مرجع نمی‌تواند خالی باشد",
    formulaOrRefStringInvalid: "فرمول یا رشته مرجع نامعتبر است",
    defaultName: "DefinedName",
    updateButton: "به‌روزرسانی",
    deleteButton: "حذف",
    deleteConfirmText: "آیا مطمئن هستید که می‌خواهید این نام تعریف شده را حذف کنید؟",
    nameConflict: "نام با نام تابع در تضاد است",
    nameInvalid: "نام نامعتبر است",
    nameSheetConflict: "نام با نام برگه در تضاد است"
  },
  uploadLoading: {
    loading: "در حال بارگیری...، باقی مانده",
    // 正在上传，当前剩余
    error: "خطا"
    // 加载失败
  },
  permission: {
    toolbarMenu: "محافظت",
    panel: {
      title: "محافظت از سطرها و ستون‌ها",
      name: "نام",
      protectedRange: "محدوده محافظت شده",
      permissionDirection: "شرح مجوز",
      permissionDirectionPlaceholder: "شرح مجوز را وارد کنید",
      editPermission: "ویرایش مجوزها",
      onlyICanEdit: "فقط من می‌توانم ویرایش کنم",
      designedUserCanEdit: "کاربران مشخص شده می‌توانند ویرایش کنند",
      viewPermission: "مجوز مشاهده",
      othersCanView: "دیگران می‌توانند مشاهده کنند",
      noOneElseCanView: "هیچ کس دیگری نمی‌تواند مشاهده کند",
      designedPerson: "افراد مشخص شده",
      addPerson: "اضافه کردن شخص",
      canEdit: "می‌تواند ویرایش کند",
      canView: "می‌تواند مشاهده کند",
      delete: "حذف",
      currentSheet: "برگه فعلی",
      allSheet: "همه برگه‌ها",
      edit: "ویرایش",
      Print: "چاپ",
      Comment: "نظر",
      Copy: "کپی",
      SetCellStyle: "تنظیم سبک سلول",
      SetCellValue: "تنظیم مقدار سلول",
      SetHyperLink: "تنظیم هایپرلینک",
      Sort: "مرتب‌سازی",
      Filter: "فیلتر",
      PivotTable: "جدول محوری",
      FloatImage: "تصویر شناور",
      RowHeightColWidth: "ارتفاع سطر و عرض ستون",
      RowHeightColWidthReadonly: "ارتفاع سطر و عرض ستون فقط خواندنی",
      FilterReadonly: "فیلتر فقط خواندنی",
      nameError: "نام نمی‌تواند خالی باشد",
      created: "ایجاد شده",
      iCanEdit: "من می‌توانم ویرایش کنم",
      iCanNotEdit: "من نمی‌توانم ویرایش کنم",
      iCanView: "من می‌توانم مشاهده کنم",
      iCanNotView: "من نمی‌توانم مشاهده کنم",
      emptyRangeError: "محدوده نمی‌تواند خالی باشد",
      rangeOverlapError: "محدوده نمی‌تواند همپوشانی داشته باشد",
      rangeOverlapOverPermissionError: "محدوده نمی‌تواند با محدوده‌ای که دارای همان مجوز است همپوشانی داشته باشد",
      InsertHyperlink: "درج هایپرلینک",
      SetRowStyle: "تنظیم سبک سطر",
      SetColumnStyle: "تنظیم سبک ستون",
      InsertColumn: "درج ستون",
      InsertRow: "درج سطر",
      DeleteRow: "حذف سطر",
      DeleteColumn: "حذف ستون",
      EditExtraObject: "ویرایش شیء اضافی"
    },
    dialog: {
      allowUserToEdit: "اجازه ویرایش به کاربر",
      allowedPermissionType: "انواع مجوز مجاز",
      setCellValue: "تنظیم مقدار سلول",
      setCellStyle: "تنظیم سبک سلول",
      copy: "کپی",
      alert: "هشدار",
      search: "جستجو",
      alertContent: "این محدوده محافظت شده است و در حال حاضر هیچ مجوز ویرایشی در دسترس نیست. اگر نیاز به ویرایش دارید، لطفا با سازنده تماس بگیرید.",
      userEmpty: "هیچ شخص تعیین شده‌ای وجود ندارد، لینک اشتراک را برای دعوت افراد خاص ارسال کنید.",
      listEmpty: "شما هیچ محدوده یا برگه‌ای را به عنوان محافظت شده تنظیم نکرده‌اید.",
      commonErr: "محدوده محافظت شده است و شما مجوز این عملیات را ندارید. برای ویرایش، لطفا با سازنده تماس بگیرید.",
      editErr: "محدوده محافظت شده است و شما مجوز ویرایش را ندارید. برای ویرایش، لطفا با سازنده تماس بگیرید.",
      pasteErr: "محدوده محافظت شده است و شما مجوز چسباندن را ندارید. برای چسباندن، لطفا با سازنده تماس بگیرید.",
      setStyleErr: "محدوده محافظت شده است و شما مجوز تنظیم سبک‌ها را ندارید. برای تنظیم سبک‌ها، لطفا با سازنده تماس بگیرید.",
      copyErr: "محدوده محافظت شده است و شما مجوز کپی را ندارید. برای کپی کردن، لطفا با سازنده تماس بگیرید.",
      workbookCopyErr: "کاربرگ محافظت شده است و شما مجوز کپی کردن را ندارید. برای کپی کردن، لطفا با سازنده تماس بگیرید.",
      setRowColStyleErr: "محدوده محافظت شده است و شما مجوز تنظیم سبک‌های سطر و ستون را ندارید. برای تنظیم سبک‌های سطر و ستون، لطفا با سازنده تماس بگیرید.",
      moveRowColErr: "محدوده محافظت شده است و شما مجوز جابجایی سطرها و ستون‌ها را ندارید. برای جابجایی سطرها و ستون‌ها، لطفا با سازنده تماس بگیرید.",
      moveRangeErr: "محدوده محافظت شده است و شما مجوز جابجایی انتخاب را ندارید. برای جابجایی انتخاب، لطفا با سازنده تماس بگیرید.",
      autoFillErr: "محدوده محافظت شده است و شما مجوز پر کردن خودکار را ندارید. برای استفاده از پر کردن خودکار، لطفا با سازنده تماس بگیرید.",
      filterErr: "محدوده محافظت شده است و شما مجوز فیلتر کردن را ندارید. برای فیلتر کردن، لطفا با سازنده تماس بگیرید.",
      operatorSheetErr: "کاربرگ محافظت شده است و شما مجوز کار با کاربرگ را ندارید. برای کار با کاربرگ، لطفا با سازنده تماس بگیرید.",
      insertOrDeleteMoveRangeErr: "محدوده درج شده یا حذف شده با محدوده محافظت شده تلاقی دارد و این عملیات در حال حاضر پشتیبانی نمی‌شود.",
      printErr: "کاربرگ محافظت شده است و شما مجوز چاپ را ندارید. برای چاپ، لطفا با سازنده تماس بگیرید.",
      formulaErr: "محدوده یا محدوده مرجع محافظت شده است و شما مجوز ویرایش را ندارید. برای ویرایش، لطفا با سازنده تماس بگیرید.",
      hyperLinkErr: "محدوده محافظت شده است و شما مجوز تنظیم هایپرلینک را ندارید. برای تنظیم هایپرلینک، لطفا با سازنده تماس بگیرید."
    },
    button: {
      confirm: "تایید",
      cancel: "انصراف",
      addNewPermission: "اضافه کردن مجوز جدید"
    }
  }
};
export {
  e as default
};
