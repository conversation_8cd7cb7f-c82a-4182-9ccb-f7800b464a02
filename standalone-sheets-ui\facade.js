// Mock @univerjs/core dependencies
const CommandType = { COMMAND: 0, OPERATION: 1, MUTATION: 2 };
const createInterceptorKey = (key) => Symbol(key);
const ColorKit = class { static toHex() { return '#000000'; } };
const RANGE_TYPE = { RANGE: 0, ROW: 1, COLUMN: 2, ALL: 3 };
const convertCellToRange = (cell) => ({ startRow: cell.row, endRow: cell.row, startColumn: cell.col, endColumn: cell.col });
const UniverInstanceType = { UNIVER_SHEET: 'UNIVER_SHEET', UNIVER_DOC: 'UNIVER_DOC', UNIVER_SLIDE: 'UNIVER_SLIDE' };
const IUniverInstanceService = Symbol('IUniverInstanceService');
const Quantity = class { constructor(value, unit) { this.value = value; this.unit = unit; } };
const Disposable = class { constructor() { this._disposed = false; } dispose() { this._disposed = true; } };
const toDisposable = (fn) => ({ dispose: fn });
const Tools = { isDefine: (v) => v != null, isString: (v) => typeof v === 'string', isNumber: (v) => typeof v === 'number' };
const createIdentifier = (name) => Symbol(name);
const InterceptorManager = class { constructor() {} };
const ThemeService = Symbol('ThemeService');
const Inject = (token) => (target, propertyKey, parameterIndex) => {};
const Injector = class { constructor() { this._services = new Map(); } get(token) { return this._services.get(token); } add(deps) {} };
const IConfigService = Symbol('IConfigService');
const generateRandomId = () => Math.random().toString(36).substr(2, 9);
const ObjectMatrix = class { constructor() { this._matrix = {}; } getValue(row, col) { return this._matrix[row]?.[col]; } setValue(row, col, value) { if (!this._matrix[row]) this._matrix[row] = {}; this._matrix[row][col] = value; } };
const Direction = { UP: 0, DOWN: 1, LEFT: 2, RIGHT: 3 };
const isFormulaString = (str) => typeof str === 'string' && str.startsWith('=');
const isFormulaId = (str) => typeof str === 'string' && str.startsWith('=');
const CellValueType = { STRING: 0, NUMBER: 1, BOOLEAN: 2, FORCE_STRING: 3 };
const numfmt = { format: () => '' };
const ICommandService = Symbol('ICommandService');
const IUndoRedoService = Symbol('IUndoRedoService');
const Rectangle = class { constructor(startRow = 0, startColumn = 0, endRow = 0, endColumn = 0) { this.startRow = startRow; this.startColumn = startColumn; this.endRow = endRow; this.endColumn = endColumn; } };
const sequenceExecute = async (commands) => { for (const cmd of commands) { if (typeof cmd === 'function') await cmd(); } };
const FOCUSING_SHEET = 'FOCUSING_SHEET';
const FOCUSING_UNIVER_EDITOR = 'FOCUSING_UNIVER_EDITOR';
const EDITOR_ACTIVATED = 'EDITOR_ACTIVATED';
const FOCUSING_COMMON_DRAWINGS = 'FOCUSING_COMMON_DRAWINGS';
const FOCUSING_FX_BAR_EDITOR = 'FOCUSING_FX_BAR_EDITOR';
const FOCUSING_EDITOR_INPUT_FORMULA = 'FOCUSING_EDITOR_INPUT_FORMULA';
const FOCUSING_EDITOR_STANDALONE = 'FOCUSING_EDITOR_STANDALONE';
const LRUMap = class { constructor(max) { this.max = max; this.cache = new Map(); } get(key) { return this.cache.get(key); } set(key, value) { this.cache.set(key, value); } };
const BaselineOffset = { NORMAL: 0, SUPER: 1, SUB: 2 };
const BooleanNumber = { TRUE: 1, FALSE: 0 };
const DataStreamTreeTokenType = { TEXT: 0, PARAGRAPH: 1, SECTION_BREAK: 2, TABLE: 3, TABLE_ROW: 4, TABLE_CELL: 5, CUSTOM_BLOCK: 6, CUSTOM_RANGE: 7, CUSTOM_DECORATOR: 8 };
const skipParseTagNames = ['br', 'img', 'hr', 'input', 'meta', 'link'];
const CustomRangeType = { HYPERLINK: 0, MENTION: 1 };
const DEFAULT_WORKSHEET_ROW_HEIGHT = 20;
const ILogService = Symbol('ILogService');
const LocaleService = class { constructor() {} t(key) { return key; } };
const ErrorService = class { constructor() {} };
const CellModeEnum = { EDIT: 0, VIEW: 1 };
const isNotNullOrUndefined = (value) => value != null;
const extractPureTextFromCell = (cell) => cell?.v || '';
const getReverseDirection = (direction) => direction === Direction.UP ? Direction.DOWN : direction === Direction.DOWN ? Direction.UP : direction === Direction.LEFT ? Direction.RIGHT : Direction.LEFT;
const IContextService = Symbol('IContextService');
const Workbook = class { constructor() { this._worksheets = []; } getActiveSheet() { return this._worksheets[0] || { getSheetId: () => 'sheet1' }; } getUnitId() { return 'workbook1'; } };
const DOCS_NORMAL_EDITOR_UNIT_ID_KEY = 'DOCS_NORMAL_EDITOR_UNIT_ID_KEY';
const Optional = (token) => (target, propertyKey, parameterIndex) => {};
const DOCS_FORMULA_BAR_EDITOR_UNIT_ID_KEY = 'DOCS_FORMULA_BAR_EDITOR_UNIT_ID_KEY';
const RxDisposable = class { constructor() { this._disposables = []; } disposeWithMe(disposable) { this._disposables.push(disposable); } dispose() { this._disposables.forEach(d => d.dispose()); } };
const DisposableCollection = class { constructor() { this._disposables = []; } add(disposable) { this._disposables.push(disposable); } dispose() { this._disposables.forEach(d => d.dispose()); } };
const IPermissionService = Symbol('IPermissionService');
const fromEventSubject = (element, event) => ({ subscribe: () => ({ unsubscribe: () => {} }) });
const sortRules = (rules) => rules.sort();
const nameCharacterCheck = (name) => /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(name);
const throttle = (fn, delay) => { let timeout; return (...args) => { clearTimeout(timeout); timeout = setTimeout(() => fn(...args), delay); }; };
const debounce = (fn, delay) => { let timeout; return (...args) => { clearTimeout(timeout); timeout = setTimeout(() => fn(...args), delay); }; };
const VerticalAlign = { TOP: 0, MIDDLE: 1, BOTTOM: 2 };
const HorizontalAlign = { LEFT: 0, CENTER: 1, RIGHT: 2, JUSTIFY: 3 };
const WrapStrategy = { UNSPECIFIED: 0, OVERFLOW: 1, WRAP: 2, CLIP: 3 };
const LocaleType = { ZH_CN: 'zh-CN', EN_US: 'en-US' };
const FOCUSING_EDITOR_BUT_HIDDEN = 'FOCUSING_EDITOR_BUT_HIDDEN';
const DEFAULT_EMPTY_DOCUMENT_VALUE = { id: 'default', body: { dataStream: '', textRuns: [], paragraphs: [{ startIndex: 0 }] } };
const isTextFormat = (format) => typeof format === 'object' && format !== null;
const PresetListType = { BULLET_LIST: 0, ORDER_LIST: 1, CHECK_LIST: 2 };
const UserManagerService = class { constructor() {} };
const DEFAULT_STYLES = {};
const FontWeight = { NORMAL: 400, BOLD: 700 };
const FontItalic = { NORMAL: 0, ITALIC: 1 };
const composeStyles = (...styles) => Object.assign({}, ...styles);
const Dimension = class { constructor(width, height) { this.width = width; this.height = height; } };
const get = (obj, path) => path.split('.').reduce((o, p) => o?.[p], obj);
const BorderStyleTypes = { NONE: 0, THIN: 1, MEDIUM: 2, THICK: 3, DOTTED: 4, DASHED: 5, DASH_DOT: 6, DASH_DOT_DOT: 7, DOUBLE: 8 };
const AbsoluteRefType = { NONE: 0, ABSOLUTE: 1, RELATIVE: 2, MIXED: 3 };
const NilCommand = { id: 'core.command.nil' };
const splitIntoGrid = (data, rows, cols) => [];
const UndoCommandId = 'core.command.undo';
const willLoseNumericPrecision = (num) => !Number.isInteger(num) && num.toString().includes('.');
const Range = class { constructor(startRow, startColumn, endRow, endColumn) { this.startRow = startRow; this.startColumn = startColumn; this.endRow = endRow; this.endColumn = endColumn; } };
const cellToRange = (cell) => new Range(cell.row, cell.col, cell.row, cell.col);
const handleStyleToString = (style) => JSON.stringify(style);
const DEFAULT_WORKSHEET_COLUMN_WIDTH_KEY = 'DEFAULT_WORKSHEET_COLUMN_WIDTH_KEY';
const DEFAULT_WORKSHEET_COLUMN_WIDTH = 100;
const DocumentFlavor = { TRADITIONAL: 0, MODERN: 1 };
const isRealNum = (value) => typeof value === 'number' && !isNaN(value);
const InterceptorEffectEnum = { BEFORE: 0, AFTER: 1 };
const IAuthzIoService = Symbol('IAuthzIoService');
const isValidRange = (range) => range && typeof range === 'object';
const dayjs = { format: () => '', parse: () => new Date() };
const BuildTextUtils = class { constructor() {} };
const DependentOn = (...deps) => (target) => target;
const Plugin = class { constructor() { this._injector = new Injector(); } onStarting() {} onReady() {} onRendered() {} dispose() {} };
const merge = (...objects) => Object.assign({}, ...objects);
const registerDependencies = (injector, deps) => { deps.forEach(dep => injector.add(dep)); };
const mergeOverrideWithDependencies = (base, overrides) => [...base, ...overrides];
const touchDependencies = (injector, deps) => { deps.forEach(dep => injector.get(dep[0])); };
const set = (obj, path, value) => { const keys = path.split('.'); let current = obj; for (let i = 0; i < keys.length - 1; i++) { if (!current[keys[i]]) current[keys[i]] = {}; current = current[keys[i]]; } current[keys[keys.length - 1]] = value; };
// Mock @univerjs/core/facade - 请根据需要实现具体功能
// 原导入: import { FUniver as q, FEventName as de } from "@univerjs/core/facade";
// Mock @univerjs/docs - 请根据需要实现具体功能
// 原导入: import { RichTextEditingMutation as he } from "@univerjs/docs";
// Mock @univerjs/engine-render - 请根据需要实现具体功能
// 原导入: import { IRenderManagerService as v, DeviceInputEventType as z, SHEET_VIEWPORT_KEY as le, sheetContentViewportKeys as ge } from "@univerjs/engine-render";
// Mock @univerjs/sheets - 请根据需要实现具体功能
// 原导入: import { SheetsSelectionsService as ue, COMMAND_LISTENER_SKELETON_CHANGE as ve, getSkeletonChangedEffectedRange as Se, SetWorksheetRowIsAutoHeightCommand as me, InterceptCellContentPriority as Ce, SheetInterceptorService as we, INTERCEPTOR_POINT as be } from "@univerjs/sheets";
// Mock @univerjs/sheets-ui - 请根据需要实现具体功能
// 原导入: import { SetCellEditVisibleOperation as f, IEditorBridgeService as R, SetZoomRatioCommand as A, HoverManagerService as p, DragManagerService as M, SheetScrollManagerService as x, SheetPasteShortKeyCommand as L, ISheetClipboardService as Q, SheetSkeletonManagerService as I, SHEET_VIEW_KEY as w, ISheetSelectionRenderService as y, SheetsScrollRenderController as Ee, SetWorksheetColAutoWidthCommand as pe, SetColumnHeaderHeightCommand as fe, SetRowHeaderWidthCommand as ke, SheetCanvasPopManagerService as N, CellAlertManagerService as Ie, IMarkSelectionService as _e, ISheetCellDropdownManagerService as Pe } from "@univerjs/sheets-ui";
// Mock @univerjs/sheets/facade - 请根据需要实现具体功能
// 原导入: import { FSheetHooks as $, FWorkbook as ee, FWorksheet as te, FPermission as re, FRange as ne } from "@univerjs/sheets/facade";
// Mock @univerjs/ui - 请根据需要实现具体功能
// 原导入: import { KeyCode as D, CutCommand as F, CopyCommand as V, PasteCommand as Z, IClipboardInterfaceService as Re, PLAIN_TEXT_CLIPBOARD_MIME_TYPE as K, HTML_CLIPBOARD_MIME_TYPE as Y, supportClipboardAPI as X, ISidebarService as He, IDialogService as Me, ComponentManager as J } from "@univerjs/ui";
const { u: filter, ye: combineLatest } = require("rxjs");
class Ue extends q {
  // eslint-disable-next-line max-lines-per-function
  _initSheetUIEvent(e) {
    const r = e.get(_);
    this.registerEventHandler(
      this.Event.BeforeSheetEditStart,
      () => r.beforeCommandExecuted((t) => {
        if (t.id !== f.id) return;
        const i = this.getActiveSheet();
        if (!i) return;
        const { workbook: o, worksheet: s } = i, d = e.get(R), l = t.params, { visible: h, keycode: g, eventType: a } = l, n = d.getEditLocation();
        if (h) {
          const c = {
            row: n.row,
            column: n.column,
            eventType: a,
            keycode: g,
            workbook: o,
            worksheet: s,
            isZenEditor: !1
          };
          if (this.fireEvent(this.Event.BeforeSheetEditStart, c), c.cancel)
            throw new E();
        }
      })
    ), this.registerEventHandler(
      this.Event.BeforeSheetEditEnd,
      () => r.beforeCommandExecuted((t) => {
        if (t.id !== f.id) return;
        const i = this.getActiveSheet();
        if (!i) return;
        const { workbook: o, worksheet: s } = i, d = e.get(R), l = e.get(j), h = t.params, { visible: g, keycode: a, eventType: n } = h, c = d.getEditLocation();
        if (!g) {
          const P = {
            row: c.row,
            column: c.column,
            eventType: n,
            keycode: a,
            workbook: o,
            worksheet: s,
            isZenEditor: !1,
            value: W.create(l.getUnit(T).getSnapshot()),
            isConfirm: a !== D.ESC
          };
          if (this.fireEvent(this.Event.BeforeSheetEditEnd, P), P.cancel)
            throw new E();
        }
      })
    ), this.registerEventHandler(
      this.Event.SheetEditStarted,
      () => r.onCommandExecuted((t) => {
        if (t.id !== f.id) return;
        const i = this.getCommandSheetTarget(t);
        if (!i) return;
        const { workbook: o, worksheet: s } = i, d = e.get(R), l = t.params, { visible: h, keycode: g, eventType: a } = l, n = d.getEditLocation();
        if (h) {
          const c = {
            row: n.row,
            column: n.column,
            eventType: a,
            keycode: g,
            workbook: o,
            worksheet: s,
            isZenEditor: !1
          };
          this.fireEvent(this.Event.SheetEditStarted, c);
        }
      })
    ), this.registerEventHandler(
      this.Event.SheetEditEnded,
      () => r.onCommandExecuted((t) => {
        if (t.id !== f.id) return;
        const i = this.getCommandSheetTarget(t);
        if (!i) return;
        const { workbook: o, worksheet: s } = i, d = e.get(R), l = t.params, { visible: h, keycode: g, eventType: a } = l, n = d.getEditLocation();
        if (!h) {
          const c = {
            row: n.row,
            column: n.column,
            eventType: a,
            keycode: g,
            workbook: o,
            worksheet: s,
            isZenEditor: !1,
            isConfirm: g !== D.ESC
          };
          this.fireEvent(this.Event.SheetEditEnded, c);
        }
      })
    ), this.registerEventHandler(
      this.Event.SheetEditChanging,
      () => r.onCommandExecuted((t) => {
        if (t.id !== he.id) return;
        const i = this.getActiveSheet();
        if (!i) return;
        const { workbook: o, worksheet: s } = i, d = e.get(R), l = e.get(j), h = t.params;
        if (!d.isVisible().visible) return;
        const { unitId: g } = h;
        if (g === T) {
          const { row: a, column: n } = d.getEditLocation(), c = {
            workbook: o,
            worksheet: s,
            row: a,
            column: n,
            value: W.create(l.getUnit(T).getSnapshot()),
            isZenEditor: !1
          };
          this.fireEvent(this.Event.SheetEditChanging, c);
        }
      })
    ), this.registerEventHandler(
      this.Event.BeforeSheetZoomChange,
      () => r.beforeCommandExecuted((t) => {
        if (t.id !== A.id) return;
        const i = this.getCommandSheetTarget(t);
        if (!i) return;
        const { workbook: o, worksheet: s } = i, d = {
          zoom: t.params.zoomRatio,
          workbook: o,
          worksheet: s
        };
        if (this.fireEvent(this.Event.BeforeSheetZoomChange, d), d.cancel)
          throw new E();
      })
    ), this.registerEventHandler(
      this.Event.SheetZoomChanged,
      () => r.onCommandExecuted((t) => {
        if (t.id !== A.id) return;
        const i = this.getCommandSheetTarget(t);
        if (!i) return;
        const { workbook: o, worksheet: s } = i;
        this.fireEvent(this.Event.SheetZoomChanged, {
          zoom: s.getZoom(),
          workbook: o,
          worksheet: s
        });
      })
    );
  }
  // eslint-disable-next-line max-lines-per-function
  _initObserverListener(e) {
    const r = e.get(v), t = e.get(ie), i = new B();
    this.disposeWithMe(t.lifecycle$.subscribe((l) => {
      if (l !== O.Rendered) return;
      i.dispose();
      const h = e.get(p), g = e.get(M);
      h && (this.registerEventHandler(
        this.Event.CellClicked,
        () => {
          var a;
          return (a = h.currentClickedCell$) == null ? void 0 : a.pipe(u((n) => !!n)).subscribe((n) => {
            const c = this.getSheetTarget(n.location.unitId, n.location.subUnitId);
            c && this.fireEvent(this.Event.CellClicked, {
              ...c,
              ...n,
              row: n.location.row,
              column: n.location.col
            });
          });
        }
      ), this.registerEventHandler(
        this.Event.CellHover,
        () => {
          var a;
          return (a = h.currentRichText$) == null ? void 0 : a.pipe(u((n) => !!n)).subscribe((n) => {
            const c = this.getSheetTarget(n.unitId, n.subUnitId);
            c && this.fireEvent(this.Event.CellHover, {
              ...c,
              ...n,
              row: n.row,
              column: n.col
            });
          });
        }
      ), this.registerEventHandler(
        this.Event.CellPointerDown,
        () => {
          var a;
          return (a = h.currentPointerDownCell$) == null ? void 0 : a.pipe(u((n) => !!n)).subscribe((n) => {
            const c = this.getSheetTarget(n.unitId, n.subUnitId);
            c && this.fireEvent(this.Event.CellPointerDown, {
              ...c,
              ...n,
              row: n.row,
              column: n.col
            });
          });
        }
      ), this.registerEventHandler(
        this.Event.CellPointerUp,
        () => {
          var a;
          return (a = h.currentPointerUpCell$) == null ? void 0 : a.pipe(u((n) => !!n)).subscribe((n) => {
            const c = this.getSheetTarget(n.unitId, n.subUnitId);
            c && this.fireEvent(this.Event.CellPointerUp, {
              ...c,
              ...n,
              row: n.row,
              column: n.col
            });
          });
        }
      ), this.registerEventHandler(
        this.Event.CellPointerMove,
        () => {
          var a;
          return (a = h.currentCellPosWithEvent$) == null ? void 0 : a.pipe(u((n) => !!n)).subscribe((n) => {
            const c = this.getSheetTarget(n.unitId, n.subUnitId);
            c && this.fireEvent(this.Event.CellPointerMove, {
              ...c,
              ...n,
              row: n.row,
              column: n.col
            });
          });
        }
      ), this.registerEventHandler(
        this.Event.DragOver,
        () => {
          var a;
          return (a = g.currentCell$) == null ? void 0 : a.pipe(u((n) => !!n)).subscribe((n) => {
            const c = this.getSheetTarget(n.location.unitId, n.location.subUnitId);
            c && this.fireEvent(this.Event.DragOver, {
              ...c,
              ...n,
              row: n.location.row,
              column: n.location.col
            });
          });
        }
      ), this.registerEventHandler(
        this.Event.Drop,
        () => {
          var a;
          return (a = g.endCell$) == null ? void 0 : a.pipe(u((n) => !!n)).subscribe((n) => {
            const c = this.getSheetTarget(n.location.unitId, n.location.subUnitId);
            c && this.fireEvent(this.Event.Drop, {
              ...c,
              ...n,
              row: n.location.row,
              column: n.location.col
            });
          });
        }
      ), this.registerEventHandler(
        this.Event.RowHeaderClick,
        () => {
          var a;
          return (a = h.currentRowHeaderClick$) == null ? void 0 : a.pipe(u((n) => !!n)).subscribe((n) => {
            const c = this.getSheetTarget(n.unitId, n.subUnitId);
            c && this.fireEvent(this.Event.RowHeaderClick, {
              ...c,
              row: n.index
            });
          });
        }
      ), this.registerEventHandler(
        this.Event.RowHeaderPointerDown,
        () => {
          var a;
          return (a = h.currentRowHeaderPointerDown$) == null ? void 0 : a.pipe(u((n) => !!n)).subscribe((n) => {
            const c = this.getSheetTarget(n.unitId, n.subUnitId);
            c && this.fireEvent(this.Event.RowHeaderPointerDown, {
              ...c,
              row: n.index
            });
          });
        }
      ), this.registerEventHandler(
        this.Event.RowHeaderPointerUp,
        () => {
          var a;
          return (a = h.currentRowHeaderPointerUp$) == null ? void 0 : a.pipe(u((n) => !!n)).subscribe((n) => {
            const c = this.getSheetTarget(n.unitId, n.subUnitId);
            c && this.fireEvent(this.Event.RowHeaderPointerUp, {
              ...c,
              row: n.index
            });
          });
        }
      ), this.registerEventHandler(
        this.Event.RowHeaderHover,
        () => {
          var a;
          return (a = h.currentHoveredRowHeader$) == null ? void 0 : a.pipe(u((n) => !!n)).subscribe((n) => {
            const c = this.getSheetTarget(n.unitId, n.subUnitId);
            c && this.fireEvent(this.Event.RowHeaderHover, {
              ...c,
              row: n.index
            });
          });
        }
      ), this.registerEventHandler(
        this.Event.ColumnHeaderClick,
        () => {
          var a;
          return (a = h.currentColHeaderClick$) == null ? void 0 : a.pipe(u((n) => !!n)).subscribe((n) => {
            const c = this.getSheetTarget(n.unitId, n.subUnitId);
            c && this.fireEvent(this.Event.ColumnHeaderClick, {
              ...c,
              column: n.index
            });
          });
        }
      ), this.registerEventHandler(
        this.Event.ColumnHeaderPointerDown,
        () => {
          var a;
          return (a = h.currentColHeaderPointerDown$) == null ? void 0 : a.pipe(u((n) => !!n)).subscribe((n) => {
            const c = this.getSheetTarget(n.unitId, n.subUnitId);
            c && this.fireEvent(this.Event.ColumnHeaderPointerDown, {
              ...c,
              column: n.index
            });
          });
        }
      ), this.registerEventHandler(
        this.Event.ColumnHeaderPointerUp,
        () => {
          var a;
          return (a = h.currentColHeaderPointerUp$) == null ? void 0 : a.pipe(u((n) => !!n)).subscribe((n) => {
            const c = this.getSheetTarget(n.unitId, n.subUnitId);
            c && this.fireEvent(this.Event.ColumnHeaderPointerUp, {
              ...c,
              column: n.index
            });
          });
        }
      ), this.registerEventHandler(
        this.Event.ColumnHeaderHover,
        () => {
          var a;
          return (a = h.currentHoveredColHeader$) == null ? void 0 : a.pipe(u((n) => !!n)).subscribe((n) => {
            const c = this.getSheetTarget(n.unitId, n.subUnitId);
            c && this.fireEvent(this.Event.ColumnHeaderHover, {
              ...c,
              column: n.index
            });
          });
        }
      ));
    })), this.disposeWithMe(i);
    const o = /* @__PURE__ */ new Map();
    let s;
    const d = ye([
      r.created$,
      t.lifecycle$
    ]);
    this.disposeWithMe(d.subscribe(([l, h]) => {
      var P;
      if (l.type === oe.UNIVER_SHEET && (s = l), h <= O.Rendered || !s) return;
      const g = new B(), a = this.getWorkbook(s.unitId);
      if (!a) return;
      o.get(s.unitId) && ((P = o.get(s.unitId)) == null || P.dispose()), o.set(s.unitId, g);
      const n = s.with(x), c = s.with(ue);
      g.add(this.registerEventHandler(
        this.Event.Scroll,
        () => n.validViewportScrollInfo$.subscribe((S) => {
          S && this.fireEvent(this.Event.Scroll, {
            workbook: a,
            worksheet: a.getActiveSheet(),
            ...S
          });
        })
      )), g.add(this.registerEventHandler(
        this.Event.SelectionMoveStart,
        () => c.selectionMoveStart$.subscribe((S) => {
          var C;
          this.fireEvent(this.Event.SelectionMoveStart, {
            workbook: a,
            worksheet: a.getActiveSheet(),
            selections: (C = S == null ? void 0 : S.map((k) => k.range)) != null ? C : []
          });
        })
      )), g.add(this.registerEventHandler(
        this.Event.SelectionMoving,
        () => c.selectionMoving$.subscribe((S) => {
          var C;
          this.fireEvent(this.Event.SelectionMoving, {
            workbook: a,
            worksheet: a.getActiveSheet(),
            selections: (C = S == null ? void 0 : S.map((k) => k.range)) != null ? C : []
          });
        })
      )), g.add(this.registerEventHandler(
        this.Event.SelectionMoveEnd,
        () => c.selectionMoveEnd$.subscribe((S) => {
          var C;
          this.fireEvent(this.Event.SelectionMoveEnd, {
            workbook: a,
            worksheet: a.getActiveSheet(),
            selections: (C = S == null ? void 0 : S.map((k) => k.range)) != null ? C : []
          });
        })
      )), g.add(this.registerEventHandler(
        this.Event.SelectionChanged,
        () => c.selectionChanged$.subscribe((S) => {
          var C;
          this.fireEvent(this.Event.SelectionChanged, {
            workbook: a,
            worksheet: a.getActiveSheet(),
            selections: (C = S == null ? void 0 : S.map((k) => k.range)) != null ? C : []
          });
        })
      )), s = null, this.disposeWithMe(g);
    })), this.disposeWithMe(r.disposed$.subscribe((l) => {
      var h;
      (h = o.get(l)) == null || h.dispose(), o.delete(l);
    })), this.disposeWithMe(() => {
      o.forEach((l) => {
        l.dispose();
      });
    });
  }
  /**
   * @ignore
   */
  _initialize(e) {
    this._initSheetUIEvent(e), this._initObserverListener(e);
    const r = e.get(_);
    this.registerEventHandler(
      this.Event.BeforeClipboardChange,
      () => r.beforeCommandExecuted((t) => {
        switch (t.id) {
          case V.id:
          case F.id:
            this._beforeClipboardChange();
            break;
        }
      })
    ), this.registerEventHandler(
      this.Event.ClipboardChanged,
      () => r.onCommandExecuted((t) => {
        switch (t.id) {
          case V.id:
          case F.id:
            this._clipboardChanged();
            break;
        }
      })
    ), this.registerEventHandler(
      this.Event.BeforeClipboardPaste,
      () => r.beforeCommandExecuted((t) => {
        switch (t.id) {
          case L.id:
            this._beforeClipboardPaste(t.params);
            break;
          case Z.id:
            this._beforeClipboardPasteAsync();
            break;
        }
      })
    ), this.registerEventHandler(
      this.Event.ClipboardPasted,
      () => r.onCommandExecuted((t) => {
        switch (t.id) {
          case L.id:
            this._clipboardPaste(t.params);
            break;
          case Z.id:
            this._clipboardPasteAsync();
            break;
        }
      })
    ), this.registerEventHandler(
      this.Event.SheetSkeletonChanged,
      () => r.onCommandExecuted((t) => {
        if (ve.indexOf(t.id) > -1) {
          const i = this.getActiveSheet();
          if (!i) return;
          const o = Se(t, i.worksheet.getMaxColumns()).map((s) => {
            var d, l;
            return (l = (d = this.getWorkbook(s.unitId)) == null ? void 0 : d.getSheetBySheetId(s.subUnitId)) == null ? void 0 : l.getRange(s.range);
          }).filter(Boolean);
          if (!o.length) return;
          this.fireEvent(this.Event.SheetSkeletonChanged, {
            workbook: i.workbook,
            worksheet: i.worksheet,
            payload: t,
            skeleton: i.worksheet.getSkeleton(),
            effectedRanges: o
          });
        }
      })
    );
  }
  _generateClipboardCopyParam() {
    const e = this.getActiveWorkbook(), r = e == null ? void 0 : e.getActiveSheet(), t = e == null ? void 0 : e.getActiveRange();
    if (!e || !r || !t)
      return;
    const o = this._injector.get(Q).generateCopyContent(e.getId(), r.getSheetId(), t.getRange());
    if (!o)
      return;
    const { html: s, plain: d } = o;
    return {
      workbook: e,
      worksheet: r,
      text: d,
      html: s,
      fromSheet: r,
      fromRange: t
    };
  }
  _beforeClipboardChange() {
    const e = this._generateClipboardCopyParam();
    if (e && (this.fireEvent(this.Event.BeforeClipboardChange, e), e.cancel))
      throw new E();
  }
  _clipboardChanged() {
    const e = this._generateClipboardCopyParam();
    e && this.fireEvent(this.Event.ClipboardChanged, e);
  }
  _generateClipboardPasteParam(e) {
    if (!e)
      return;
    const { htmlContent: r, textContent: t } = e, i = this.getActiveWorkbook(), o = i == null ? void 0 : i.getActiveSheet();
    return !i || !o ? void 0 : {
      workbook: i,
      worksheet: o,
      text: t,
      html: r
    };
  }
  async _generateClipboardPasteParamAsync() {
    const e = this.getActiveWorkbook(), r = e == null ? void 0 : e.getActiveSheet();
    if (!e || !r)
      return;
    const o = (await this._injector.get(Re).read())[0];
    let s;
    if (o) {
      const d = o.types, l = d.indexOf(K) !== -1 ? await o.getType(K).then((g) => g && g.text()) : "", h = d.indexOf(Y) !== -1 ? await o.getType(Y).then((g) => g && g.text()) : "";
      s = {
        workbook: e,
        worksheet: r,
        text: l,
        html: h
      };
    }
    return s;
  }
  _beforeClipboardPaste(e) {
    const r = this._generateClipboardPasteParam(e);
    if (r && (this.fireEvent(this.Event.BeforeClipboardPaste, r), r.cancel))
      throw new E();
  }
  _clipboardPaste(e) {
    const r = this._generateClipboardPasteParam(e);
    if (r && (this.fireEvent(this.Event.ClipboardPasted, r), r.cancel))
      throw new E();
  }
  async _beforeClipboardPasteAsync() {
    if (!X()) {
      this._injector.get(U).warn("[Facade]: The navigator object only supports the browser environment");
      return;
    }
    const e = await this._generateClipboardPasteParamAsync();
    if (e && (this.fireEvent(this.Event.BeforeClipboardPaste, e), e.cancel))
      throw new E();
  }
  async _clipboardPasteAsync() {
    if (!X()) {
      this._injector.get(U).warn("[Facade]: The navigator object only supports the browser environment");
      return;
    }
    const e = await this._generateClipboardPasteParamAsync();
    if (e && (this.fireEvent(this.Event.ClipboardPasted, e), e.cancel))
      throw new E();
  }
  customizeColumnHeader(e) {
    var h, g;
    const r = this.getActiveWorkbook();
    if (!r) {
      console.error("WorkBook not exist");
      return;
    }
    const t = r == null ? void 0 : r.getId(), i = this._injector.get(v), o = r.getActiveSheet(), s = o.getSheetId(), d = i.getRenderById(t);
    d && ((h = e.headerStyle) != null && h.size) && (d.with(I).setColumnHeaderSize(d, s, (g = e.headerStyle) == null ? void 0 : g.size), o == null || o.refreshCanvas()), this._getSheetRenderComponent(t, w.COLUMN).setCustomHeader(e), o == null || o.refreshCanvas();
  }
  customizeRowHeader(e) {
    const r = this.getActiveWorkbook();
    if (!r) {
      console.error("WorkBook not exist");
      return;
    }
    const t = r == null ? void 0 : r.getId();
    this._getSheetRenderComponent(t, w.ROW).setCustomHeader(e);
  }
  registerSheetRowHeaderExtension(e, ...r) {
    const t = this._getSheetRenderComponent(e, w.ROW), i = t.register(...r);
    return m(() => {
      i.dispose(), t.makeDirty(!0);
    });
  }
  registerSheetColumnHeaderExtension(e, ...r) {
    const t = this._getSheetRenderComponent(e, w.COLUMN), i = t.register(...r);
    return m(() => {
      i.dispose(), t.makeDirty(!0);
    });
  }
  registerSheetMainExtension(e, ...r) {
    const t = this._getSheetRenderComponent(e, w.MAIN), i = t.register(...r);
    return m(() => {
      i.dispose(), t.makeDirty(!0);
    });
  }
  /**
   * Get sheet render component from render by unitId and view key.
   * @private
   * @param {string} unitId The unit id of the spreadsheet.
   * @param {SHEET_VIEW_KEY} viewKey The view key of the spreadsheet.
   * @returns {Nullable<RenderComponentType>} The render component.
   */
  _getSheetRenderComponent(e, r) {
    const i = this._injector.get(v).getRenderById(e);
    if (!i)
      throw new Error(`Render Unit with unitId ${e} not found`);
    const { components: o } = i, s = o.get(r);
    if (!s)
      throw new Error("Render component not found");
    return s;
  }
  /**
   * Get sheet hooks.
   * @returns {FSheetHooks} FSheetHooks instance
   */
  getSheetHooks() {
    return this._injector.createInstance($);
  }
}
q.extend(Ue);
class xe extends ee {
  openSiderbar(e) {
    return this._logDeprecation("openSiderbar"), this._injector.get(He).open(e);
  }
  openDialog(e) {
    this._logDeprecation("openDialog");
    const t = this._injector.get(Me).open({
      ...e,
      onClose: () => {
        t.dispose();
      }
    });
    return t;
  }
  customizeColumnHeader(e) {
    const r = this._workbook.getUnitId();
    this._getSheetRenderComponent(r, w.COLUMN).setCustomHeader(e);
  }
  customizeRowHeader(e) {
    const r = this._workbook.getUnitId();
    this._getSheetRenderComponent(r, w.ROW).setCustomHeader(e);
  }
  /**
   * Get sheet render component from render by unitId and view key.
   * @private
   * @param {string} unitId The unit id of the spreadsheet.
   * @param {SHEET_VIEW_KEY} viewKey The view key of the spreadsheet.
   * @returns {Nullable<RenderComponentType>} The render component.
   */
  _getSheetRenderComponent(e, r) {
    const i = this._injector.get(v).getRenderById(e);
    if (!i)
      throw new Error(`Render Unit with unitId ${e} not found`);
    const { components: o } = i, s = o.get(r);
    if (!s)
      throw new Error("Render component not found");
    return s;
  }
  _logDeprecation(e) {
    this._injector.get(U).warn("[FWorkbook]", `${e} is deprecated. Please use the function of the same name on "FUniver".`);
  }
  generateCellParams(e) {
    const r = this.getActiveSheet();
    return {
      row: e.row,
      column: e.col,
      workbook: this,
      worksheet: r
    };
  }
  onCellClick(e) {
    const r = this._injector.get(p);
    return m(
      r.currentClickedCell$.pipe(u((t) => !!t)).subscribe((t) => {
        e(t);
      })
    );
  }
  onCellHover(e) {
    const r = this._injector.get(p);
    return m(
      r.currentRichText$.pipe(u((t) => !!t)).subscribe(e)
    );
  }
  onCellPointerDown(e) {
    const r = this._injector.get(p);
    return m(
      r.currentPointerDownCell$.subscribe(e)
    );
  }
  onCellPointerUp(e) {
    const r = this._injector.get(p);
    return m(
      r.currentPointerUpCell$.subscribe(e)
    );
  }
  onCellPointerMove(e) {
    const r = this._injector.get(p);
    return m(
      r.currentCellPosWithEvent$.pipe(u((t) => !!t)).subscribe((t) => {
        e(t, t.event);
      })
    );
  }
  onDragOver(e) {
    const r = this._injector.get(M);
    return m(
      r.currentCell$.pipe(u((t) => !!t)).subscribe((t) => {
        e(t);
      })
    );
  }
  onDrop(e) {
    const r = this._injector.get(M);
    return m(
      r.endCell$.pipe(u((t) => !!t)).subscribe((t) => {
        e(t);
      })
    );
  }
  startEditing() {
    return this._injector.get(_).syncExecuteCommand(f.id, {
      eventType: z.Dblclick,
      unitId: this._workbook.getUnitId(),
      visible: !0
    });
  }
  async endEditing(e) {
    return this._injector.get(_).syncExecuteCommand(f.id, {
      eventType: z.Keyboard,
      keycode: e ? D.ENTER : D.ESC,
      visible: !1,
      unitId: this._workbook.getUnitId()
    }), await se(0), !0;
  }
  endEditingAsync(e = !0) {
    return this.endEditing(e);
  }
  abortEditingAsync() {
    return this.endEditingAsync(!1);
  }
  /**
   * Get scroll state of specified sheet.
   * @param {string} sheetId - sheet id
   * @returns {IScrollState} scroll state
   * @example
   * ``` ts
   * univerAPI.getActiveWorkbook().getScrollStateBySheetId($sheetId)
   * ```
   */
  getScrollStateBySheetId(e) {
    const r = this._workbook.getUnitId(), i = this._injector.get(v).getRenderById(r);
    return i ? i.with(x).getScrollStateByParam({ unitId: r, sheetId: e }) : null;
  }
  disableSelection() {
    const e = this._workbook.getUnitId(), t = this._injector.get(v).getRenderById(e);
    return t && t.with(y).disableSelection(), this;
  }
  enableSelection() {
    const e = this._workbook.getUnitId(), t = this._injector.get(v).getRenderById(e);
    return t && t.with(y).enableSelection(), this;
  }
  transparentSelection() {
    const e = this._workbook.getUnitId(), t = this._injector.get(v).getRenderById(e);
    return t && t.with(y).transparentSelection(), this;
  }
  showSelection() {
    const e = this._workbook.getUnitId(), t = this._injector.get(v).getRenderById(e);
    return t && t.with(y).showSelection(), this;
  }
}
ee.extend(xe);
class De extends te {
  refreshCanvas() {
    const e = this._injector.get(v), r = this._fWorkbook.id, t = e.getRenderById(r);
    if (!t)
      throw new Error(`Render Unit with unitId ${r} not found`);
    t.with(I).reCalculate();
    const i = t.mainComponent;
    if (!i)
      throw new Error("Main component not found");
    return i.makeDirty(), this;
  }
  zoom(e) {
    const r = this._injector.get(_), t = Math.min(Math.max(e, 0.1), 4);
    return r.executeCommand(A.id, {
      unitId: this._workbook.getUnitId(),
      subUnitId: this._worksheet.getSheetId(),
      zoomRatio: t
    }), this;
  }
  getZoom() {
    return this._worksheet.getZoomRatio();
  }
  getVisibleRange() {
    const e = this._workbook.getUnitId(), t = this._injector.get(v).getRenderById(e);
    let i = {
      startColumn: 0,
      startRow: 0,
      endColumn: 0,
      endRow: 0
    };
    if (!t) return i;
    const s = t.with(I).getCurrentSkeleton();
    if (!s) return i;
    const d = s == null ? void 0 : s.getVisibleRanges();
    if (!d) return i;
    i = s.getVisibleRangeByViewport(le.VIEW_MAIN);
    for (const [l, h] of d)
      ge.indexOf(l) !== -1 && (i.startColumn = Math.min(i.startColumn, h.startColumn), i.startRow = Math.min(i.startRow, h.startRow), i.endColumn = Math.max(i.endColumn, h.endColumn), i.endRow = Math.max(i.endRow, h.endRow));
    return i;
  }
  scrollToCell(e, r) {
    const t = this._workbook.getUnitId(), o = this._injector.get(v).getRenderById(t);
    return o && (o == null ? void 0 : o.with(Ee)).scrollToCell(e, r), this;
  }
  getScrollState() {
    const e = {
      offsetX: 0,
      offsetY: 0,
      sheetViewStartColumn: 0,
      sheetViewStartRow: 0
    }, r = this._workbook.getUnitId(), t = this._worksheet.getSheetId(), o = this._injector.get(v).getRenderById(r);
    return o && o.with(x).getScrollStateByParam({ unitId: r, sheetId: t }) || e;
  }
  onScroll(e) {
    var o;
    const r = this._workbook.getUnitId(), i = (o = this._injector.get(v).getRenderById(r)) == null ? void 0 : o.with(x);
    if (i) {
      const s = i.validViewportScrollInfo$.subscribe((d) => {
        e(d);
      });
      return m(s);
    }
    return m(() => {
    });
  }
  getSkeleton() {
    var r, t;
    const e = (r = this._injector.get(v).getRenderById(this._workbook.getUnitId())) == null ? void 0 : r.with(I);
    return (t = e == null ? void 0 : e.getWorksheetSkeleton(this._worksheet.getSheetId())) == null ? void 0 : t.skeleton;
  }
  autoResizeColumn(e) {
    return this.autoResizeColumns(e, 1);
  }
  autoResizeColumns(e, r) {
    const t = this._workbook.getUnitId(), i = this._worksheet.getSheetId(), o = [
      {
        startColumn: e,
        endColumn: e + r - 1,
        startRow: 0,
        endRow: this._worksheet.getRowCount() - 1
      }
    ];
    return this._commandService.syncExecuteCommand(pe.id, {
      unitId: t,
      subUnitId: i,
      ranges: o
    }), this;
  }
  setColumnAutoWidth(e, r) {
    return this.autoResizeColumns(e, r);
  }
  autoResizeRows(e, r) {
    const t = this._workbook.getUnitId(), i = this._worksheet.getSheetId(), o = [
      {
        startRow: e,
        endRow: e + r - 1,
        startColumn: 0,
        endColumn: this._worksheet.getColumnCount() - 1
      }
    ];
    return this._commandService.syncExecuteCommand(me.id, {
      unitId: t,
      subUnitId: i,
      ranges: o
    }), this;
  }
  customizeColumnHeader(e) {
    var d, l;
    const r = this._workbook.getUnitId(), t = this._worksheet.getSheetId(), o = this._injector.get(v).getRenderById(r);
    o && ((d = e.headerStyle) != null && d.size) && o.with(I).setColumnHeaderSize(o, t, (l = e.headerStyle) == null ? void 0 : l.size), this._getSheetRenderComponent(r, w.COLUMN).setCustomHeader(e, t);
  }
  customizeRowHeader(e) {
    var d, l;
    const r = this._workbook.getUnitId(), t = this._worksheet.getSheetId(), o = this._injector.get(v).getRenderById(r);
    o && ((d = e.headerStyle) != null && d.size) && o.with(I).setRowHeaderSize(o, t, (l = e.headerStyle) == null ? void 0 : l.size), this._getSheetRenderComponent(r, w.ROW).setCustomHeader(e, t);
  }
  setColumnHeaderHeight(e) {
    const r = this._workbook.getUnitId(), t = this._worksheet.getSheetId();
    return this._commandService.executeCommand(fe.id, {
      unitId: r,
      subUnitId: t,
      size: e
    }), this;
  }
  setRowHeaderWidth(e) {
    const r = this._workbook.getUnitId(), t = this._worksheet.getSheetId();
    return this._commandService.executeCommand(ke.id, {
      unitId: r,
      subUnitId: t,
      size: e
    }), this;
  }
  /**
   * Get sheet render component from render by unitId and view key.
   * @private
   * @param {string} unitId The unit id of the spreadsheet.
   * @param {SHEET_VIEW_KEY} viewKey The view key of the spreadsheet.
   * @returns {Nullable<RenderComponentType>} The render component.
   */
  _getSheetRenderComponent(e, r) {
    const i = this._injector.get(v).getRenderById(e);
    if (!i)
      throw new Error(`Render Unit with unitId ${e} not found`);
    const { components: o } = i, s = o.get(r);
    if (!s)
      throw new Error("Render component not found");
    return s;
  }
}
te.extend(De);
class Te extends re {
  setPermissionDialogVisible(e) {
    this._permissionService.setShowComponents(e);
  }
}
re.extend(Te);
class Be extends $ {
  onCellPointerMove(e) {
    return m(this._injector.get(p).currentPosition$.subscribe(e));
  }
  onCellPointerOver(e) {
    return m(this._injector.get(p).currentCell$.subscribe(e));
  }
  onCellDragOver(e) {
    return m(this._injector.get(M).currentCell$.subscribe(e));
  }
  onCellDrop(e) {
    return m(this._injector.get(M).endCell$.subscribe(e));
  }
  onCellRender(e, r = ae.Style, t = Ce.DATA_VALIDATION) {
    return this._injector.get(we).intercept(be.CELL_CONTENT, {
      effect: r,
      handler: (i, o, s) => (i && !i.customRender && e && (i.customRender = [...e]), s(i)),
      priority: t
    });
  }
  onBeforeCellEdit(e) {
    return this._injector.get(_).beforeCommandExecuted((r) => {
      const t = r.params;
      r.id === f.id && t.visible && e(t);
    });
  }
  onAfterCellEdit(e) {
    return this._injector.get(_).onCommandExecuted((r) => {
      const t = r.params;
      r.id === f.id && !t.visible && e(t);
    });
  }
}
$.extend(Be);
const H = {
  CellClicked: "CellClicked",
  CellPointerDown: "CellPointerDown",
  CellPointerUp: "CellPointerUp",
  CellPointerMove: "CellPointerMove",
  CellHover: "CellHover"
};
class Ae {
  get BeforeClipboardChange() {
    return "BeforeClipboardChange";
  }
  get ClipboardChanged() {
    return "ClipboardChanged";
  }
  get BeforeClipboardPaste() {
    return "BeforeClipboardPaste";
  }
  get ClipboardPasted() {
    return "ClipboardPasted";
  }
  get BeforeSheetEditStart() {
    return "BeforeSheetEditStart";
  }
  get SheetEditStarted() {
    return "SheetEditStarted";
  }
  get SheetEditChanging() {
    return "SheetEditChanging";
  }
  get BeforeSheetEditEnd() {
    return "BeforeSheetEditEnd";
  }
  get SheetEditEnded() {
    return "SheetEditEnded";
  }
  get CellClicked() {
    return H.CellClicked;
  }
  get CellHover() {
    return H.CellHover;
  }
  get CellPointerDown() {
    return H.CellPointerDown;
  }
  get CellPointerUp() {
    return H.CellPointerUp;
  }
  get CellPointerMove() {
    return H.CellPointerMove;
  }
  get DragOver() {
    return "DragOver";
  }
  get Drop() {
    return "Drop";
  }
  get Scroll() {
    return "Scroll";
  }
  get SelectionMoveStart() {
    return "SelectionMoveStart";
  }
  get SelectionChanged() {
    return "SelectionChanged";
  }
  get SelectionMoving() {
    return "SelectionMoving";
  }
  get SelectionMoveEnd() {
    return "SelectionMoveEnd";
  }
  get RowHeaderClick() {
    return "RowHeaderClick";
  }
  get RowHeaderPointerDown() {
    return "RowHeaderPointerDown";
  }
  get RowHeaderPointerUp() {
    return "RowHeaderPointerUp";
  }
  get RowHeaderHover() {
    return "RowHeaderHover";
  }
  get ColumnHeaderClick() {
    return "ColumnHeaderClick";
  }
  get ColumnHeaderPointerDown() {
    return "ColumnHeaderPointerDown";
  }
  get ColumnHeaderPointerUp() {
    return "ColumnHeaderPointerUp";
  }
  get ColumnHeaderHover() {
    return "ColumnHeaderHover";
  }
  get SheetSkeletonChanged() {
    return "SheetSkeletonChanged";
  }
  get BeforeSheetZoomChange() {
    return "BeforeSheetZoomChange";
  }
  get SheetZoomChanged() {
    return "SheetZoomChanged";
  }
}
de.extend(Ae);
class $e extends ne {
  getCell() {
    var d;
    const e = this._injector.get(v), r = this._injector.get(U), t = this._workbook.getUnitId(), i = this._worksheet.getSheetId(), o = e.getRenderById(t), s = (d = o == null ? void 0 : o.with(I).getSkeletonParam(i)) == null ? void 0 : d.skeleton;
    if (!s)
      throw r.error("[Facade]: `FRange.getCell` can only be called in current worksheet"), new Error("`FRange.getCell` can only be called in current worksheet");
    return s.getCellWithCoordByIndex(this._range.startRow, this._range.startColumn);
  }
  getCellRect() {
    const { startX: e, startY: r, endX: t, endY: i } = this.getCell(), o = { x: e, y: r, width: t - e, height: i - r, top: r, left: e, bottom: i, right: t };
    return { ...o, toJSON: () => JSON.stringify(o) };
  }
  generateHTML() {
    var t;
    const r = this._injector.get(Q).generateCopyContent(
      this._workbook.getUnitId(),
      this._worksheet.getSheetId(),
      this._range
    );
    return (t = r == null ? void 0 : r.html) != null ? t : "";
  }
  attachPopup(e) {
    var s, d, l;
    e.direction = (s = e.direction) != null ? s : "horizontal", e.extraProps = (d = e.extraProps) != null ? d : {}, e.offset = (l = e.offset) != null ? l : [0, 0];
    const { key: r, disposableCollection: t } = G(e, this._injector.get(J)), o = this._injector.get(N).attachPopupToCell(
      this._range.startRow,
      this._range.startColumn,
      { ...e, componentKey: r },
      this.getUnitId(),
      this._worksheet.getSheetId()
    );
    return o ? (t.add(o), t) : (t.dispose(), null);
  }
  attachAlertPopup(e) {
    const r = this._injector.get(Ie), t = {
      workbook: this._workbook,
      worksheet: this._worksheet,
      row: this._range.startRow,
      col: this._range.startColumn,
      unitId: this.getUnitId(),
      subUnitId: this._worksheet.getSheetId()
    };
    return r.showAlert({
      ...e,
      location: t
    }), {
      dispose: () => {
        r.removeAlert(e.key);
      }
    };
  }
  /**
   * attachDOMPopup
   * @param popup
   * @returns {IDisposable} disposable
      let sheet = univerAPI.getActiveWorkbook().getActiveSheet();
      let range = sheet.getRange(2, 2, 3, 3);
      univerAPI.getActiveWorkbook().setActiveRange(range);
      let disposable = range.attachDOMPopup({
      componentKey: 'univer.sheet.single-dom-popup',
      extraProps: { alert: { type: 0, title: 'This is an Info', message: 'This is an info message' } },
      });
   */
  attachRangePopup(e) {
    var s, d, l;
    e.direction = (s = e.direction) != null ? s : "top-center", e.extraProps = (d = e.extraProps) != null ? d : {}, e.offset = (l = e.offset) != null ? l : [0, 0];
    const { key: r, disposableCollection: t } = G(e, this._injector.get(J)), o = this._injector.get(N).attachRangePopup(
      this._range,
      { ...e, componentKey: r },
      this.getUnitId(),
      this._worksheet.getSheetId()
    );
    return o ? (t.add(o), t) : (t.dispose(), null);
  }
  highlight(e, r) {
    const t = this._injector.get(_e), i = t.addShape({ range: this._range, style: e, primary: r });
    if (!i)
      throw new Error("Failed to highlight current range");
    return m(() => {
      t.removeShape(i);
    });
  }
  showDropdown(e) {
    return this._injector.get(Pe).showDropdown(e);
  }
}
ne.extend($e);
function G(b, e) {
  const { componentKey: r, isVue3: t } = b;
  let i;
  const o = new B();
  return typeof r == "string" ? i = r : (i = `External_${ce(6)}`, o.add(e.register(i, r, { framework: t ? "vue3" : "react" }))), {
    key: i,
    disposableCollection: o
  };
}
export {
  G as transformComponentKey
};
