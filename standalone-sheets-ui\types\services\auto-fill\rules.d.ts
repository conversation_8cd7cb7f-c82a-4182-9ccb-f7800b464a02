import { IAutoFillRule } from './type';
export declare const dateRule: IAutoFillRule;
export declare const numberRule: IAutoFillRule;
export declare const otherRule: IAutoFillRule;
export declare const extendNumberRule: IAutoFillRule;
export declare const chnNumberRule: IAutoFillRule;
export declare const chnWeek2Rule: IAutoFillRule;
export declare const chnWeek3Rule: IAutoFillRule;
export declare const loopSeriesRule: IAutoFillRule;
export declare function reverseIfNeed<T>(data: T[], reverse: boolean): T[];
