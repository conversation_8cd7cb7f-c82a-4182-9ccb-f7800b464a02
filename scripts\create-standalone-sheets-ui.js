#!/usr/bin/env node

/**
 * 创建独立的 sheets-ui 包，移除所有 @univerjs 依赖
 * 使用方法: node scripts/create-standalone-sheets-ui.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 源目录和目标目录
const sourceDir = 'packages/sheets-ui/lib';
const targetDir = 'standalone-sheets-ui';

// 需要替换的依赖映射
const dependencyMocks = {
    '@univerjs/telemetry': `
// Mock telemetry service for standalone usage
const ITelemetryService = Symbol('ITelemetryService');
const CC = ITelemetryService;
`,
    '@univerjs/docs-ui': `
// Mock @univerjs/docs-ui dependencies
const DOCS_VIEW_KEY = 'DOCS_VIEW_KEY';
const SetInlineFormatBoldCommand = { id: 'docs.command.set-inline-format-bold' };
const SetInlineFormatItalicCommand = { id: 'docs.command.set-inline-format-italic' };
const SetInlineFormatUnderlineCommand = { id: 'docs.command.set-inline-format-underline' };
const SetInlineFormatStrikethroughCommand = { id: 'docs.command.set-inline-format-strikethrough' };
const SetInlineFormatSubscriptCommand = { id: 'docs.command.set-inline-format-subscript' };
const SetInlineFormatSuperscriptCommand = { id: 'docs.command.set-inline-format-superscript' };
const SetInlineFormatFontSizeCommand = { id: 'docs.command.set-inline-format-font-size' };
const SetInlineFormatFontFamilyCommand = { id: 'docs.command.set-inline-format-font-family' };
const SetInlineFormatTextColorCommand = { id: 'docs.command.set-inline-format-text-color' };
const IEditorService = Symbol('IEditorService');
const getCanvasOffsetByEngine = () => ({ x: 0, y: 0 });
const VIEWPORT_KEY = 'VIEWPORT_KEY';
const DOCS_COMPONENT_MAIN_LAYER_INDEX = 10;
const DocSelectionRenderService = class { constructor() {} };
const BreakLineCommand = { id: 'docs.command.break-line' };
const DeleteLeftCommand = { id: 'docs.command.delete-left' };
const DeleteRightCommand = { id: 'docs.command.delete-right' };
const ReplaceSnapshotCommand = { id: 'docs.command.replace-snapshot' };
const MoveSelectionOperation = { id: 'docs.operation.move-selection' };
const MoveCursorOperation = { id: 'docs.operation.move-cursor' };
const CoverContentCommand = { id: 'docs.command.cover-content' };
const NodePositionConvertToCursor = () => ({ index: 0, isBack: false });
const getLineBounding = () => ({ top: 0, bottom: 0, left: 0, right: 0 });
const DOC_VERTICAL_PADDING = 10;
const SetInlineFormatCommand = { id: 'docs.command.set-inline-format' };
const IMEInputCommand = { id: 'docs.command.ime-input' };
const InsertCommand = { id: 'docs.command.insert' };
const convertBodyToHtml = () => '';
`,
    '@univerjs/core': `
// Mock @univerjs/core dependencies
const CommandType = { COMMAND: 0, OPERATION: 1, MUTATION: 2 };
const createInterceptorKey = (key) => Symbol(key);
const ColorKit = class { static toHex() { return '#000000'; } };
const RANGE_TYPE = { RANGE: 0, ROW: 1, COLUMN: 2, ALL: 3 };
const convertCellToRange = (cell) => ({ startRow: cell.row, endRow: cell.row, startColumn: cell.col, endColumn: cell.col });
const UniverInstanceType = { UNIVER_SHEET: 'UNIVER_SHEET', UNIVER_DOC: 'UNIVER_DOC', UNIVER_SLIDE: 'UNIVER_SLIDE' };
const IUniverInstanceService = Symbol('IUniverInstanceService');
const Quantity = class { constructor(value, unit) { this.value = value; this.unit = unit; } };
const Disposable = class { constructor() { this._disposed = false; } dispose() { this._disposed = true; } };
const toDisposable = (fn) => ({ dispose: fn });
const Tools = { isDefine: (v) => v != null, isString: (v) => typeof v === 'string', isNumber: (v) => typeof v === 'number' };
const createIdentifier = (name) => Symbol(name);
const InterceptorManager = class { constructor() {} };
const ThemeService = Symbol('ThemeService');
const Inject = (token) => (target, propertyKey, parameterIndex) => {};
const Injector = class { constructor() { this._services = new Map(); } get(token) { return this._services.get(token); } add(deps) {} };
const IConfigService = Symbol('IConfigService');
const generateRandomId = () => Math.random().toString(36).substr(2, 9);
const ObjectMatrix = class { constructor() { this._matrix = {}; } getValue(row, col) { return this._matrix[row]?.[col]; } setValue(row, col, value) { if (!this._matrix[row]) this._matrix[row] = {}; this._matrix[row][col] = value; } };
const Direction = { UP: 0, DOWN: 1, LEFT: 2, RIGHT: 3 };
const isFormulaString = (str) => typeof str === 'string' && str.startsWith('=');
const isFormulaId = (str) => typeof str === 'string' && str.startsWith('=');
const CellValueType = { STRING: 0, NUMBER: 1, BOOLEAN: 2, FORCE_STRING: 3 };
const numfmt = { format: () => '' };
const ICommandService = Symbol('ICommandService');
const IUndoRedoService = Symbol('IUndoRedoService');
const Rectangle = class { constructor(startRow = 0, startColumn = 0, endRow = 0, endColumn = 0) { this.startRow = startRow; this.startColumn = startColumn; this.endRow = endRow; this.endColumn = endColumn; } };
const sequenceExecute = async (commands) => { for (const cmd of commands) { if (typeof cmd === 'function') await cmd(); } };
const FOCUSING_SHEET = 'FOCUSING_SHEET';
const FOCUSING_UNIVER_EDITOR = 'FOCUSING_UNIVER_EDITOR';
const EDITOR_ACTIVATED = 'EDITOR_ACTIVATED';
const FOCUSING_COMMON_DRAWINGS = 'FOCUSING_COMMON_DRAWINGS';
const FOCUSING_FX_BAR_EDITOR = 'FOCUSING_FX_BAR_EDITOR';
const FOCUSING_EDITOR_INPUT_FORMULA = 'FOCUSING_EDITOR_INPUT_FORMULA';
const FOCUSING_EDITOR_STANDALONE = 'FOCUSING_EDITOR_STANDALONE';
const LRUMap = class { constructor(max) { this.max = max; this.cache = new Map(); } get(key) { return this.cache.get(key); } set(key, value) { this.cache.set(key, value); } };
const BaselineOffset = { NORMAL: 0, SUPER: 1, SUB: 2 };
const BooleanNumber = { TRUE: 1, FALSE: 0 };
const DataStreamTreeTokenType = { TEXT: 0, PARAGRAPH: 1, SECTION_BREAK: 2, TABLE: 3, TABLE_ROW: 4, TABLE_CELL: 5, CUSTOM_BLOCK: 6, CUSTOM_RANGE: 7, CUSTOM_DECORATOR: 8 };
const skipParseTagNames = ['br', 'img', 'hr', 'input', 'meta', 'link'];
const CustomRangeType = { HYPERLINK: 0, MENTION: 1 };
const DEFAULT_WORKSHEET_ROW_HEIGHT = 20;
const ILogService = Symbol('ILogService');
const LocaleService = class { constructor() {} t(key) { return key; } };
const ErrorService = class { constructor() {} };
const CellModeEnum = { EDIT: 0, VIEW: 1 };
const isNotNullOrUndefined = (value) => value != null;
const extractPureTextFromCell = (cell) => cell?.v || '';
const getReverseDirection = (direction) => direction === Direction.UP ? Direction.DOWN : direction === Direction.DOWN ? Direction.UP : direction === Direction.LEFT ? Direction.RIGHT : Direction.LEFT;
const IContextService = Symbol('IContextService');
const Workbook = class { constructor() { this._worksheets = []; } getActiveSheet() { return this._worksheets[0] || { getSheetId: () => 'sheet1' }; } getUnitId() { return 'workbook1'; } };
const DOCS_NORMAL_EDITOR_UNIT_ID_KEY = 'DOCS_NORMAL_EDITOR_UNIT_ID_KEY';
const Optional = (token) => (target, propertyKey, parameterIndex) => {};
const DOCS_FORMULA_BAR_EDITOR_UNIT_ID_KEY = 'DOCS_FORMULA_BAR_EDITOR_UNIT_ID_KEY';
const RxDisposable = class { constructor() { this._disposables = []; } disposeWithMe(disposable) { this._disposables.push(disposable); } dispose() { this._disposables.forEach(d => d.dispose()); } };
const DisposableCollection = class { constructor() { this._disposables = []; } add(disposable) { this._disposables.push(disposable); } dispose() { this._disposables.forEach(d => d.dispose()); } };
const IPermissionService = Symbol('IPermissionService');
const fromEventSubject = (element, event) => ({ subscribe: () => ({ unsubscribe: () => {} }) });
const sortRules = (rules) => rules.sort();
const nameCharacterCheck = (name) => /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(name);
const throttle = (fn, delay) => { let timeout; return (...args) => { clearTimeout(timeout); timeout = setTimeout(() => fn(...args), delay); }; };
const debounce = (fn, delay) => { let timeout; return (...args) => { clearTimeout(timeout); timeout = setTimeout(() => fn(...args), delay); }; };
const VerticalAlign = { TOP: 0, MIDDLE: 1, BOTTOM: 2 };
const HorizontalAlign = { LEFT: 0, CENTER: 1, RIGHT: 2, JUSTIFY: 3 };
const WrapStrategy = { UNSPECIFIED: 0, OVERFLOW: 1, WRAP: 2, CLIP: 3 };
const LocaleType = { ZH_CN: 'zh-CN', EN_US: 'en-US' };
const FOCUSING_EDITOR_BUT_HIDDEN = 'FOCUSING_EDITOR_BUT_HIDDEN';
const DEFAULT_EMPTY_DOCUMENT_VALUE = { id: 'default', body: { dataStream: '', textRuns: [], paragraphs: [{ startIndex: 0 }] } };
const isTextFormat = (format) => typeof format === 'object' && format !== null;
const PresetListType = { BULLET_LIST: 0, ORDER_LIST: 1, CHECK_LIST: 2 };
const UserManagerService = class { constructor() {} };
const DEFAULT_STYLES = {};
const FontWeight = { NORMAL: 400, BOLD: 700 };
const FontItalic = { NORMAL: 0, ITALIC: 1 };
const composeStyles = (...styles) => Object.assign({}, ...styles);
const Dimension = class { constructor(width, height) { this.width = width; this.height = height; } };
const get = (obj, path) => path.split('.').reduce((o, p) => o?.[p], obj);
const BorderStyleTypes = { NONE: 0, THIN: 1, MEDIUM: 2, THICK: 3, DOTTED: 4, DASHED: 5, DASH_DOT: 6, DASH_DOT_DOT: 7, DOUBLE: 8 };
const AbsoluteRefType = { NONE: 0, ABSOLUTE: 1, RELATIVE: 2, MIXED: 3 };
const NilCommand = { id: 'core.command.nil' };
const splitIntoGrid = (data, rows, cols) => [];
const UndoCommandId = 'core.command.undo';
const willLoseNumericPrecision = (num) => !Number.isInteger(num) && num.toString().includes('.');
const Range = class { constructor(startRow, startColumn, endRow, endColumn) { this.startRow = startRow; this.startColumn = startColumn; this.endRow = endRow; this.endColumn = endColumn; } };
const cellToRange = (cell) => new Range(cell.row, cell.col, cell.row, cell.col);
const handleStyleToString = (style) => JSON.stringify(style);
const DEFAULT_WORKSHEET_COLUMN_WIDTH_KEY = 'DEFAULT_WORKSHEET_COLUMN_WIDTH_KEY';
const DEFAULT_WORKSHEET_COLUMN_WIDTH = 100;
const DocumentFlavor = { TRADITIONAL: 0, MODERN: 1 };
const isRealNum = (value) => typeof value === 'number' && !isNaN(value);
const InterceptorEffectEnum = { BEFORE: 0, AFTER: 1 };
const IAuthzIoService = Symbol('IAuthzIoService');
const isValidRange = (range) => range && typeof range === 'object';
const dayjs = { format: () => '', parse: () => new Date() };
const BuildTextUtils = class { constructor() {} };
const DependentOn = (...deps) => (target) => target;
const Plugin = class { constructor() { this._injector = new Injector(); } onStarting() {} onReady() {} onRendered() {} dispose() {} };
const merge = (...objects) => Object.assign({}, ...objects);
const registerDependencies = (injector, deps) => { deps.forEach(dep => injector.add(dep)); };
const mergeOverrideWithDependencies = (base, overrides) => [...base, ...overrides];
const touchDependencies = (injector, deps) => { deps.forEach(dep => injector.get(dep[0])); };
const set = (obj, path, value) => { const keys = path.split('.'); let current = obj; for (let i = 0; i < keys.length - 1; i++) { if (!current[keys[i]]) current[keys[i]] = {}; current = current[keys[i]]; } current[keys[keys.length - 1]] = value; };
`
};

function createStandalonePackage() {
    console.log('🚀 开始创建独立的 sheets-ui 包...');

    // 检查源目录是否存在
    if (!fs.existsSync(sourceDir)) {
        console.error(`❌ 源目录不存在: ${sourceDir}`);
        console.log('请先运行 "pnpm --filter @univerjs/sheets-ui build" 来构建 sheets-ui');
        process.exit(1);
    }

    // 创建目标目录
    if (fs.existsSync(targetDir)) {
        console.log(`🗑️  删除现有目录: ${targetDir}`);
        fs.rmSync(targetDir, { recursive: true, force: true });
    }

    console.log(`📁 创建目录: ${targetDir}`);
    fs.mkdirSync(targetDir, { recursive: true });

    // 复制所有文件
    console.log('📋 复制文件...');
    copyDirectory(sourceDir, targetDir);

    // 处理依赖替换
    console.log('🔄 处理依赖替换...');
    processFiles(targetDir);

    // 创建 package.json
    console.log('📦 创建 package.json...');
    createPackageJson(targetDir);

    // 创建 README
    console.log('📝 创建 README.md...');
    createReadme(targetDir);

    console.log('✅ 独立的 sheets-ui 包创建完成!');
    console.log(`📍 位置: ${path.resolve(targetDir)}`);
    console.log('\n使用方法:');
    console.log(`import { UniverSheetsUIPlugin } from './${targetDir}';`);
}

function copyDirectory(src, dest) {
    const entries = fs.readdirSync(src, { withFileTypes: true });

    for (const entry of entries) {
        const srcPath = path.join(src, entry.name);
        const destPath = path.join(dest, entry.name);

        if (entry.isDirectory()) {
            fs.mkdirSync(destPath, { recursive: true });
            copyDirectory(srcPath, destPath);
        } else {
            fs.copyFileSync(srcPath, destPath);
        }
    }
}

function processFiles(dir) {
    const entries = fs.readdirSync(dir, { withFileTypes: true });

    for (const entry of entries) {
        const filePath = path.join(dir, entry.name);

        if (entry.isDirectory()) {
            processFiles(filePath);
        } else if (entry.name.endsWith('.js')) {
            processJavaScriptFile(filePath);
        }
    }
}

function processJavaScriptFile(filePath) {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // 替换所有 @univerjs 依赖的导入
    for (const [packageName, mockCode] of Object.entries(dependencyMocks)) {
        const importRegex = new RegExp(
            `import\\s+{[^}]+}\\s+from\\s+["']${packageName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}["'];?`,
            'g'
        );

        if (importRegex.test(content)) {
            content = content.replace(importRegex, mockCode.trim());
            modified = true;
            console.log(`  ✓ 替换 ${packageName} 在 ${path.relative(process.cwd(), filePath)}`);
        }
    }

    // 处理其他 @univerjs 包的导入（通用处理）
    const univerJsImportRegex = /import\s+{[^}]+}\s+from\s+["']@univerjs\/[^"']+["'];?/g;
    const remainingImports = content.match(univerJsImportRegex);

    if (remainingImports) {
        for (const importStatement of remainingImports) {
            const packageMatch = importStatement.match(/@univerjs\/([^"']+)/);
            if (packageMatch) {
                const packageName = `@univerjs/${packageMatch[1]}`;
                console.log(`  ⚠️  发现未处理的依赖: ${packageName} 在 ${path.relative(process.cwd(), filePath)}`);

                // 创建通用 mock
                const mockComment = `// Mock ${packageName} - 请根据需要实现具体功能\n// 原导入: ${importStatement}`;
                content = content.replace(importStatement, mockComment);
                modified = true;
            }
        }
    }

    // 将 ES 模块导入转换为 CommonJS require（针对 peer dependencies）
    const peerDependencies = ['rxjs', 'react', 'react/jsx-runtime', 'rxjs/operators'];

    for (const dep of peerDependencies) {
        const esImportRegex = new RegExp(
            `import\\s+{([^}]+)}\\s+from\\s+["']${dep.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}["'];?`,
            'g'
        );

        content = content.replace(esImportRegex, (match, imports) => {
            // 将 ES 导入转换为 CommonJS require
            const cleanImports = imports.split(',').map(imp => {
                const [imported, alias] = imp.trim().split(' as ');
                return alias ? `${alias.trim()}: ${imported.trim()}` : imported.trim();
            }).join(', ');

            return `const { ${cleanImports} } = require("${dep}");`;
        });

        if (esImportRegex.test(content)) {
            modified = true;
            console.log(`  ✓ 转换 ${dep} 导入为 CommonJS 在 ${path.relative(process.cwd(), filePath)}`);
        }
    }

    if (modified) {
        fs.writeFileSync(filePath, content);
    }
}

function createPackageJson(targetDir) {
    const packageJson = {
        name: "standalone-sheets-ui",
        version: "1.0.0",
        description: "Standalone UniverJS Sheets UI Plugin - No external @univerjs dependencies",
        main: "index.js",
        module: "es/index.js",
        types: "types/index.d.ts",
        exports: {
            ".": {
                "import": "./es/index.js",
                "require": "./cjs/index.js",
                "types": "./types/index.d.ts"
            },
            "./*": {
                "import": "./es/*",
                "require": "./cjs/*"
            },
            "./lib/*": "./lib/*"
        },
        files: [
            "**/*"
        ],
        peerDependencies: {
            "react": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0",
            "react-dom": "^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0",
            "rxjs": ">=7.0.0"
        },
        keywords: [
            "univer",
            "sheets",
            "spreadsheet",
            "ui",
            "standalone"
        ],
        author: "Your Name",
        license: "Apache-2.0",
        repository: {
            type: "git",
            url: "your-repository-url"
        }
    };

    fs.writeFileSync(
        path.join(targetDir, 'package.json'),
        JSON.stringify(packageJson, null, 2)
    );
}

function createReadme(targetDir) {
    const readme = `# Standalone Sheets UI

这是一个独立的 UniverJS Sheets UI 插件包，已移除所有 @univerjs 外部依赖。

## 安装

\`\`\`bash
# 将此目录复制到您的项目中
cp -r ${targetDir} your-project/src/
\`\`\`

## 使用

\`\`\`javascript
import { UniverSheetsUIPlugin } from './standalone-sheets-ui';

// 在您的 Univer 实例中注册插件
univer.registerPlugin(UniverSheetsUIPlugin);
\`\`\`

## 注意事项

1. 此包已将所有 @univerjs 依赖替换为 mock 实现
2. 某些功能可能需要您根据实际需求进行调整
3. 保留了 React 和 RxJS 作为 peer dependencies
4. 如果遇到功能问题，请检查相关的 mock 实现

## 依赖

- React ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
- RxJS >=7.0.0

## 许可证

Apache-2.0
`;

    fs.writeFileSync(path.join(targetDir, 'README.md'), readme);
}

// 直接运行脚本
createStandalonePackage();

export { createStandalonePackage };
