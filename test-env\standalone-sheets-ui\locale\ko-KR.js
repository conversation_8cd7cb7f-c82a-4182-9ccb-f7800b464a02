const e = {
  spreadsheetLabel: "스프레드시트",
  spreadsheetRightLabel: "추가 시트",
  toolbar: {
    undo: "실행 취소",
    redo: "다시 실행",
    formatPainter: "서식 페인트",
    font: "글꼴",
    fontSize: "글꼴 크기",
    bold: "굵게",
    italic: "기울임",
    strikethrough: "취소선",
    subscript: "아래 첨자",
    superscript: "위 첨자",
    underline: "밑줄",
    textColor: {
      main: "텍스트 색상",
      right: "색상 선택"
    },
    resetColor: "초기화",
    fillColor: {
      main: "채우기 색상",
      right: "색상 선택"
    },
    border: {
      main: "테두리",
      right: "테두리 스타일"
    },
    mergeCell: {
      main: "셀 병합",
      right: "병합 유형 선택"
    },
    horizontalAlignMode: {
      main: "가로 정렬",
      right: "정렬"
    },
    verticalAlignMode: {
      main: "세로 정렬",
      right: "정렬"
    },
    textWrapMode: {
      main: "텍스트 줄바꿈",
      right: "텍스트 줄바꿈 모드"
    },
    textRotateMode: {
      main: "텍스트 회전",
      right: "텍스트 회전 모드"
    },
    more: "더 보기",
    toggleGridlines: "눈금선 표시/숨기기"
  },
  align: {
    left: "왼쪽",
    center: "가운데",
    right: "오른쪽",
    top: "위쪽",
    middle: "가운데",
    bottom: "아래쪽"
  },
  button: {
    confirm: "확인",
    cancel: "취소",
    close: "닫기",
    update: "업데이트",
    delete: "삭제",
    insert: "삽입",
    prevPage: "이전",
    nextPage: "다음",
    total: "총계:"
  },
  punctuation: {
    tab: "탭",
    semicolon: "세미콜론",
    comma: "쉼표",
    space: "공백"
  },
  colorPicker: {
    collapse: "축소",
    customColor: "사용자 정의",
    change: "변경",
    confirmColor: "확인",
    cancelColor: "취소"
  },
  borderLine: {
    borderTop: "위쪽",
    borderBottom: "아래쪽",
    borderLeft: "왼쪽",
    borderRight: "오른쪽",
    borderNone: "없음",
    borderAll: "전체",
    borderOutside: "외부",
    borderInside: "내부",
    borderHorizontal: "가로",
    borderVertical: "세로",
    borderColor: "테두리 색상",
    borderSize: "테두리 크기",
    borderType: "테두리 유형"
  },
  merge: {
    all: "전체 병합",
    vertical: "세로 병합",
    horizontal: "가로 병합",
    cancel: "병합 해제",
    overlappingError: "겹치는 영역은 병합할 수 없습니다",
    partiallyError: "부분 병합된 셀에서는 작업할 수 없습니다",
    confirm: {
      title: "병합 시 왼쪽 위 셀 외의 값은 삭제됩니다. 계속하시겠습니까?",
      cancel: "병합 취소",
      confirm: "계속 병합",
      warning: "경고",
      dismantleMergeCellWarning: "일부 병합된 셀이 해제됩니다. 계속하시겠습니까?"
    }
  },
  filter: {
    confirm: {
      error: "There was a problem",
      notAllowedToInsertRange: "Not allowed to move cells here until filter is cleared"
    }
  },
  textWrap: {
    overflow: "넘침",
    wrap: "줄바꿈",
    clip: "자르기"
  },
  textRotate: {
    none: "없음",
    angleUp: "기울이기 (위쪽)",
    angleDown: "기울이기 (아래쪽)",
    vertical: "세로 쌓기",
    rotationUp: "위로 회전",
    rotationDown: "아래로 회전"
  },
  sheetConfig: {
    delete: "삭제",
    copy: "복사",
    rename: "이름 변경",
    changeColor: "색상 변경",
    hide: "숨기기",
    unhide: "보이기",
    moveLeft: "왼쪽으로 이동",
    moveRight: "오른쪽으로 이동",
    resetColor: "색상 초기화",
    cancelText: "취소",
    chooseText: "색상 선택",
    tipNameRepeat: "탭 페이지의 이름은 중복될 수 없습니다! 다시 작성해주세요",
    noMoreSheet: "통합 문서에는 최소한 하나의 시트가 있습니다. 선택한 시트를 삭제하려면 새 시트를 삽입하거나 숨겨진 시트를 표시해주세요.",
    confirmDelete: "삭제하시겠습니까",
    redoDelete: "Ctrl+Z로 실행 취소할 수 있습니다",
    noHide: "최소한 하나의 시트 태그를 유지해야 합니다",
    chartEditNoOpt: "차트 편집 모드에서는 이 작업을 허용하지 않습니다!",
    sheetNameErrorTitle: "문제가 발생했습니다",
    sheetNameSpecCharError: "이름은 31자를 초과할 수 없으며, 시작 또는 끝에 포함될 수 없으며, [ ] : \\ ? * /를 포함할 수 없습니다",
    sheetNameCannotIsEmptyError: "시트 이름은 비어 있을 수 없습니다.",
    sheetNameAlreadyExistsError: "시트 이름이 이미 존재합니다. 다른 이름을 입력해주세요.",
    deleteSheet: "시트 삭제",
    deleteSheetContent: "이 시트를 삭제하시겠습니까? 삭제 후에는 복구할 수 없습니다. 삭제하시겠습니까?",
    addProtectSheet: "시트 보호",
    removeProtectSheet: "시트 보호 해제",
    changeSheetPermission: "시트 권한 변경",
    viewAllProtectArea: "모든 보호 범위 보기"
  },
  rightClick: {
    copy: "복사",
    cut: "자르기",
    paste: "붙여넣기",
    pasteSpecial: "선택하여 붙여넣기",
    pasteValue: "값 붙여넣기",
    pasteFormat: "서식 붙여넣기",
    pasteColWidth: "열 너비 붙여넣기",
    pasteBesidesBorder: "테두리 스타일 붙여넣기",
    insert: "삽입",
    insertRow: "행 삽입",
    insertRowBefore: "앞에 행 삽입",
    insertRowsAfter: "아래에 행 삽입",
    insertRowsAbove: "위에 행 삽입",
    insertRowsAfterSuffix: "행 아래",
    insertRowsAboveSuffix: "행 위",
    insertColumn: "열 삽입",
    insertColumnBefore: "앞에 열 삽입",
    insertColsLeft: "왼쪽에 열 삽입",
    insertColsRight: "오른쪽에 열 삽입",
    insertColsLeftSuffix: "열 왼쪽",
    insertColsRightSuffix: "열 오른쪽",
    delete: "삭제",
    deleteCell: "셀 삭제",
    insertCell: "셀 삽입",
    deleteSelected: "선택한 항목 삭제",
    hide: "숨기기",
    hideSelected: "선택한 항목 숨기기",
    showHide: "숨겨진 항목 표시/숨기기",
    toTopAdd: "위쪽에 추가",
    toBottomAdd: "아래쪽에 추가",
    toLeftAdd: "왼쪽에 추가",
    toRightAdd: "오른쪽에 추가",
    deleteSelectedRow: "선택한 행 삭제",
    deleteSelectedColumn: "선택한 열 삭제",
    hideSelectedRow: "선택한 행 숨기기",
    showHideRow: "행 표시/숨기기",
    rowHeight: "행 높이",
    hideSelectedColumn: "선택한 열 숨기기",
    showHideColumn: "열 표시/숨기기",
    columnWidth: "열 너비",
    moveLeft: "왼쪽으로 이동",
    moveUp: "위쪽으로 이동",
    moveRight: "오른쪽으로 이동",
    moveDown: "아래쪽으로 이동",
    add: "추가",
    row: "행",
    column: "열",
    confirm: "확인",
    clearSelection: "선택 초기화",
    clearContent: "내용 초기화",
    clearFormat: "서식 초기화",
    clearAll: "모두 초기화",
    root: "루트",
    log: "로그",
    delete0: "양쪽 끝의 0 값 삭제",
    removeDuplicate: "중복 값 제거",
    byRow: "행별",
    byCol: "열별",
    generateNewMatrix: "새 행렬 생성",
    fitContent: "데이터에 맞게 조정",
    freeze: "고정",
    freezeCol: "이 열에 고정",
    freezeRow: "이 행에 고정",
    cancelFreeze: "고정 해제",
    deleteAllRowsAlert: "행을 모두 삭제할 수 없습니다",
    deleteAllColumnsAlert: "열을 모두 삭제할 수 없습니다",
    hideAllRowsAlert: "행을 모두 숨길 수 없습니다",
    hideAllColumnsAlert: "열을 모두 숨길 수 없습니다",
    protectRange: "행과 열 보호",
    editProtectRange: "Set Protection Range",
    removeProtectRange: "보호 범위 제거",
    turnOnProtectRange: "보호 범위 추가",
    viewAllProtectArea: "모든 보호 범위 보기"
  },
  info: {
    tooltip: "툴팁",
    error: "오류",
    notChangeMerge: "병합된 셀에 대해 부분적인 변경을 할 수 없습니다",
    detailUpdate: "새로 열림",
    detailSave: "로컬 캐시 복원",
    row: "",
    column: "",
    loading: "로딩중...",
    copy: "복사",
    return: "종료",
    rename: "이름 변경",
    tips: "이름 변경",
    noName: "Untitled spreadsheet",
    wait: "waiting for update",
    add: "추가",
    addLast: "아래에 더 많은 행 추가",
    backTop: "맨 위로 이동",
    // eslint-disable-next-line no-template-curly-in-string
    pageInfo: "총 ${total}, ${totalPage} 페이지, 현재 ${currentPage} 페이지",
    nextPage: "다음",
    tipInputNumber: "숫자를 입력해주세요",
    tipInputNumberLimit: "증가 범위는 1-100 사이로 제한됩니다",
    tipRowHeightLimit: "행 높이는 0 ~ 545 사이여야 합니다",
    tipColumnWidthLimit: "열 너비는 0 ~ 2038 사이여야 합니다",
    // eslint-disable-next-line no-template-curly-in-string
    pageInfoFull: "총 ${total}, ${totalPage} 페이지, 모든 데이터 표시",
    problem: "문제가 발생했습니다",
    forceStringInfo: "숫자가 텍스트로 저장되었습니다"
  },
  clipboard: {
    paste: {
      exceedMaxCells: "붙여넣기 셀 수가 최대 셀 수를 초과합니다",
      overlappingMergedCells: "붙여넣기 영역이 병합된 셀과 겹칩니다"
    },
    shortCutNotify: {
      title: "키보드 단축키를 사용하여 붙여넣으세요.",
      useShortCutInstead: "Excel 내용을 감지했습니다. 키보드 단축키를 사용하여 붙여넣으세요."
    }
  },
  statusbar: {
    sum: "Sum",
    average: "Average",
    min: "Min",
    max: "Max",
    count: "Numerical Count",
    countA: "Count",
    clickToCopy: "클릭하여 복사",
    copied: "복사됨"
  },
  autoFill: {
    copy: "셀 복사",
    series: "시리즈 채우기",
    formatOnly: "서식만 채우기",
    noFormat: "서식 없음"
  },
  rangeSelector: {
    placeholder: "범위 선택 또는 값 입력",
    tooltip: "범위 선택"
  },
  shortcut: {
    sheet: {
      "zoom-in": "확대",
      "zoom-out": "축소",
      "reset-zoom": "확대 수준 초기화",
      "select-below-cell": "아래 셀 선택",
      "select-up-cell": "위 셀 선택",
      "select-left-cell": "왼쪽 셀 선택",
      "select-right-cell": "오른쪽 셀 선택",
      "select-next-cell": "다음 셀 선택",
      "select-previous-cell": "이전 셀 선택",
      "select-up-value-cell": "값이 있는 위 셀 선택",
      "select-below-value-cell": "값이 있는 아래 셀 선택",
      "select-left-value-cell": "값이 있는 왼쪽 셀 선택",
      "select-right-value-cell": "값이 있는 오른쪽 셀 선택",
      "expand-selection-down": "선택 영역 아래로 확장",
      "expand-selection-up": "선택 영역 위로 확장",
      "expand-selection-left": "선택 영역 왼쪽으로 확장",
      "expand-selection-right": "선택 영역 오른쪽으로 확장",
      "expand-selection-to-left-gap": "선택 영역 왼쪽으로 확장",
      "expand-selection-to-below-gap": "선택 영역 아래로 확장",
      "expand-selection-to-right-gap": "선택 영역 오른쪽으로 확장",
      "expand-selection-to-up-gap": "선택 영역 위로 확장",
      "select-all": "모두 선택",
      "toggle-editing": "편집 모드 전환",
      "delete-and-start-editing": "지우고 편집 시작",
      "abort-editing": "편집 중단",
      "break-line": "줄바꿈",
      "set-bold": "굵게",
      "start-editing": "편집 시작 (편집기 선택)",
      "set-italic": "기울임",
      "set-underline": "밑줄",
      "set-strike-through": "취소선"
    }
  },
  "sheet-view": "시트 보기",
  "sheet-edit": "시트 편집",
  definedName: {
    managerTitle: "이름 관리",
    managerDescription: "셀이나 수식을 선택하고 원하는 이름을 입력하여 이름을 생성합니다.",
    addButton: "이름 추가",
    featureTitle: "이름 관리",
    ratioRange: "범위",
    ratioFormula: "수식",
    confirm: "확인",
    cancel: "취소",
    scopeWorkbook: "통합 문서",
    inputNamePlaceholder: "이름을 입력해주세요(공백 불가)",
    inputCommentPlaceholder: "주석을 입력해주세요",
    inputRangePlaceholder: "범위를 입력해주세요(공백 불가)",
    inputFormulaPlaceholder: "수식을 입력해주세요(공백 불가)",
    nameEmpty: "이름은 비어 있을 수 없습니다",
    nameDuplicate: "이름이 이미 존재합니다",
    formulaOrRefStringEmpty: "수식이나 참조 문자열은 비어 있을 수 없습니다",
    formulaOrRefStringInvalid: "잘못된 수식이나 참조 문자열",
    defaultName: "기본값",
    updateButton: "업데이트",
    deleteButton: "삭제",
    deleteConfirmText: "이 이름을 삭제하시겠습니까?",
    nameConflict: "이름이 함수 이름과 충돌합니다",
    nameInvalid: "이름이 잘못되었습니다",
    nameSheetConflict: "이름이 시트 이름과 충돌합니다"
  },
  uploadLoading: {
    loading: "로딩중...",
    error: "오류"
  },
  permission: {
    toolbarMenu: "보호",
    panel: {
      title: "행과 열 보호",
      name: "이름",
      protectedRange: "보호 범위",
      permissionDirection: "권한 설명",
      permissionDirectionPlaceholder: "권한 설명을 입력해주세요",
      editPermission: "권한 편집",
      onlyICanEdit: "본인만 편집 가능",
      designedUserCanEdit: "지정된 사용자만 편집",
      viewPermission: "권한 보기",
      othersCanView: "다른 사용자는 보기만 가능",
      noOneElseCanView: "다른 사람은 볼 수 없음",
      designedPerson: "지정된 사람",
      addPerson: "사람 추가",
      canEdit: "편집 가능",
      canView: "보기 가능",
      delete: "삭제",
      currentSheet: "현재 시트",
      allSheet: "모든 시트",
      edit: "편집",
      Print: "인쇄",
      Comment: "주석",
      Copy: "복사",
      SetCellStyle: "셀 스타일 설정",
      SetCellValue: "셀 값 설정",
      SetHyperLink: "하이퍼링크 설정",
      Sort: "정렬",
      Filter: "필터",
      PivotTable: "피벗 테이블",
      FloatImage: "플로팅 이미지",
      RowHeightColWidth: "행 높이와 열 너비",
      RowHeightColWidthReadonly: "읽기 전용 행 높이와 열 너비",
      FilterReadonly: "읽기 전용 필터",
      nameError: "이름은 비어 있을 수 없습니다",
      created: "생성됨",
      iCanEdit: "나만 편집할 수 있음",
      iCanNotEdit: "나만 편집할 수 없음",
      iCanView: "나만 볼 수 있음",
      iCanNotView: "나만 볼 수 없음",
      emptyRangeError: "범위는 비어 있을 수 없습니다",
      rangeOverlapError: "범위는 겹칠 수 없습니다",
      rangeOverlapOverPermissionError: "범위는 같은 권한을 가진 범위와 겹칠 수 없습니다",
      InsertHyperlink: "하이퍼링크 삽입",
      SetRowStyle: "행 스타일 설정",
      SetColumnStyle: "열 스타일 설정",
      InsertColumn: "열 삽입",
      InsertRow: "행 삽입",
      DeleteRow: "행 삭제",
      DeleteColumn: "열 삭제",
      EditExtraObject: "추가 객체 편집"
    },
    dialog: {
      allowUserToEdit: "사용자 편집 허용",
      allowedPermissionType: "허용된 권한 유형",
      setCellValue: "셀 값 설정",
      setCellStyle: "셀 스타일 설정",
      copy: "복사",
      alert: "경고",
      search: "검색",
      alertContent: "이 범위는 보호되어 있어 현재 편집 권한이 없습니다. 편집하려면 작성자에게 문의하세요.",
      userEmpty: "지정된 사용자가 없습니다. 특정 사용자 초대를 위해 링크를 공유하세요.",
      listEmpty: "보호된 범위나 시트가 아직 설정되지 않았습니다.",
      commonErr: "해당 범위는 보호되어 있어 이 작업을 수행할 수 없습니다. 편집하려면 작성자에게 문의하세요.",
      editErr: "해당 범위는 보호되어 있어 편집 권한이 없습니다. 편집하려면 작성자에게 문의하세요.",
      pasteErr: "해당 범위는 보호되어 있어 붙여넣기 권한이 없습니다. 붙여넣으려면 작성자에게 문의하세요.",
      setStyleErr: "해당 범위는 보호되어 있어 스타일 지정 권한이 없습니다. 스타일을 변경하려면 작성자에게 문의하세요.",
      copyErr: "해당 범위는 보호되어 있어 복사 권한이 없습니다. 복사하려면 작성자에게 문의하세요.",
      workbookCopyErr: "이 워크북은 보호되어 있어 복사할 수 없습니다. 복사하려면 작성자에게 문의하세요.",
      setRowColStyleErr: "해당 범위는 보호되어 있어 행/열 스타일 지정 권한이 없습니다. 스타일을 변경하려면 작성자에게 문의하세요.",
      moveRowColErr: "해당 범위는 보호되어 있어 행이나 열을 이동할 수 없습니다. 이동하려면 작성자에게 문의하세요.",
      moveRangeErr: "해당 범위는 보호되어 있어 선택 영역을 이동할 수 없습니다. 이동하려면 작성자에게 문의하세요.",
      autoFillErr: "해당 범위는 보호되어 있어 자동 채우기 기능을 사용할 수 없습니다. 사용하려면 작성자에게 문의하세요.",
      filterErr: "해당 범위는 보호되어 있어 필터 권한이 없습니다. 필터를 사용하려면 작성자에게 문의하세요.",
      operatorSheetErr: "이 워크시트는 보호되어 있어 작업할 수 없습니다. 작업하려면 작성자에게 문의하세요.",
      insertOrDeleteMoveRangeErr: "삽입 또는 삭제한 범위가 보호된 범위와 겹치므로, 이 작업은 현재 지원되지 않습니다.",
      printErr: "이 워크시트는 보호되어 있어 인쇄할 수 없습니다. 인쇄하려면 작성자에게 문의하세요.",
      formulaErr: "이 범위 또는 참조된 범위는 보호되어 있어 수식을 편집할 수 없습니다. 편집하려면 작성자에게 문의하세요.",
      hyperLinkErr: "해당 범위는 보호되어 있어 하이퍼링크 설정 권한이 없습니다. 설정하려면 작성자에게 문의하세요."
    },
    button: {
      confirm: "확인",
      cancel: "취소",
      addNewPermission: "새 권한 추가"
    }
  }
};
export {
  e as default
};
