# Standalone Sheets UI

这是一个独立的 UniverJS Sheets UI 插件包，已移除所有 @univerjs 外部依赖。

## 安装

```bash
# 将此目录复制到您的项目中
cp -r standalone-sheets-ui your-project/src/
```

## 使用

```javascript
import { UniverSheetsUIPlugin } from './standalone-sheets-ui';

// 在您的 Univer 实例中注册插件
univer.registerPlugin(UniverSheetsUIPlugin);
```

## 注意事项

1. 此包已将所有 @univerjs 依赖替换为 mock 实现
2. 某些功能可能需要您根据实际需求进行调整
3. 保留了 React 和 RxJS 作为 peer dependencies
4. 如果遇到功能问题，请检查相关的 mock 实现

## 依赖

- React ^16.9.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
- RxJS >=7.0.0

## 许可证

Apache-2.0
